const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");
const { b2b_payment_gateways } = require("../PLB/plb_quries");
const { payment_gateway_india_map } = require("./payment_gateway_map");

const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];

exports.marketing_revenue = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate, pstartDate, pcurrentDate } = req.body;

    let {formattedCurrentDate,formattedPreviousDateStart,formattedPreviousPeriodStart} = utc_adjust(startDate, currentDate)

    let prevpstartDate = new Date(pstartDate)

    prevpstartDate.setDate(prevpstartDate.getDate() - 1)

    prevpstartDate = prevpstartDate.toISOString().split('T')[0]

    // let cDataQB2B = {
    //   query: {
    //     bool: {
    //       must: [
    //         {
    //           range: {
    //             created_at: {
    //               gte: `${startDate}T00:00:00`,
    //               lte: `${currentDate}T00:00:00`,
    //             },
    //           },
    //         },
    //         { terms: { "payment_gateway.keyword": b2b_payment_gateways } },
    //         {
    //           terms: {
    //             txn_status: [
    //               "success",
    //               "autorenewal_success",
    //               "upgrade",
    //               "renewal",
    //               "autorenew",
    //             ],
    //           },
    //         },
    //         {
    //           match: {
    //             region: "IN",
    //           },
    //         },
    //         {
    //           match: {
    //             transaction_env: "production",
    //           },
    //         },
    //       ],
    //     },
    //   },
    //   aggs: {
    //     total_price_charged: {
    //       sum: {
    //         field: "price_charged",
    //       },
    //     },
    //   },
    //   size: 0,
    // };

    // let pDataQB2B = {
    //   query: {
    //     bool: {
    //       must: [
    //         {
    //           range: {
    //             created_at: {
    //               gte: `${startDate}T00:00:00`,
    //               lte: `${prevDate}T00:00:00`,
    //             },
    //           },
    //         },
    //         { terms: { "payment_gateway.keyword": b2b_payment_gateways } },
    //         {
    //           terms: {
    //             txn_status: [
    //               "success",
    //               "autorenewal_success",
    //               "upgrade",
    //               "renewal",
    //               "autorenew",
    //             ],
    //           },
    //         },
    //         {
    //           match: {
    //             region: "IN",
    //           },
    //         },
    //         {
    //           match: {
    //             transaction_env: "production",
    //           },
    //         },
    //       ],
    //     },
    //   },
    //   aggs: {
    //     total_price_charged: {
    //       sum: {
    //         field: "price_charged",
    //       },
    //     },
    //   },
    //   size: 0,
    // };

    let cDataQB2C = {
      "size": 0,
      "query": {
        "bool": {
          "must": [
            {
              "range": {
                "created_at": {
                  "gte": `${formattedPreviousDateStart}T18:30:00`,
                  "lt": `${formattedCurrentDate}T18:29:59`
                }
              }
            },
            {
              "term": {
                "region.keyword": "IN"
              }
            },
            {
              "terms": {
                "payment_gateway.keyword": b2c_payment_gateways
              }
            },
            {
              "terms": {
                "txn_status.keyword": [
                  "success", "autorenewal_success", "upgrade", "renewal", "autorenew"
                ]
              }
            },
            {
              "term": {
                "transaction_env.keyword": "production"
              }
            }
          ]
        }
      },
      "aggs": {
        "group_by_payment_gateway": {
          "terms": {
            "field": "payment_gateway.keyword",
            "size": 100
          },
          "aggs": {
            "total_price_charged": {
              "sum": {
                "field": "price_charged"
              }
            }
          }
        }
      }
    };

      let pDataQB2C = {
        "size": 0,
        "query": {
          "bool": {
            "must": [
              {
                "range": {
                  "created_at": {
                    "gte": `${prevpstartDate}T18:30:00`,
                    "lt": `${pcurrentDate}T18:29:59`
                  }
                }
              },
              {
                "term": {
                  "region.keyword": "IN"
                }
              },
              {
                "terms": {
                  "payment_gateway.keyword": b2c_payment_gateways
                }
              },
              {
                "terms": {
                  "txn_status.keyword": [
                    "success", "autorenewal_success", "upgrade", "renewal", "autorenew"
                  ]
                }
              },
              {
                "term": {
                  "transaction_env.keyword": "production"
                }
              }
            ]
          }
        },
        "aggs": {
          "group_by_payment_gateway": {
            "terms": {
              "field": "payment_gateway.keyword",
              "size": 100
            },
            "aggs": {
              "total_price_charged": {
                "sum": {
                  "field": "price_charged"
                }
              }
            }
          }
        }
      };
   
      // let cDataB2B = await elk_excute("/shemaroo_user_plans/_search",cDataQB2B)
      // let pDataB2B = await elk_excute("/shemaroo_user_plans/_search",pDataQB2B)
      // let cDataB2C = await elk_excute("/shemaroo_user_plans/_search",cDataQB2C)
      // let pDataB2C = await elk_excute("/shemaroo_user_plans/_search",pDataQB2C)
      // let revenueB2B = parseInt(cDataB2B.aggregations.total_price_charged.value);
      // let prevenueB2B = parseInt(pDataB2B.aggregations.total_price_charged.value);
      // let q=`select sum(total)  as t from b2b where date > '${startDate}' and date < '${currentDate}'`
      let revenueB2B = await QueryExecute(`select sum(total)  as t from b2b where date >= '${startDate}' and date <= '${currentDate}'`)
      let prevenueB2B = await QueryExecute(`select sum(total)  as t from b2b where date >= '${pstartDate}' and date <= '${pcurrentDate}'`)

      revenueB2B=parseInt(revenueB2B[0].t)
      prevenueB2B=parseInt(prevenueB2B[0].t)
      // let revenueB2C = parseInt(cDataB2C.aggregations.total_price_charged.value);
      // let prevenueB2C = parseInt(pDataB2C.aggregations.total_price_charged.value);
      
      let percentageB2B = ((revenueB2B- prevenueB2B) / prevenueB2B) * 100;

      // let percentageB2C = ((revenueB2C - prevenueB2C) / prevenueB2C) * 100;

      let cDataB2C = await elk_excute("/shemaroo_user_plans/_search",cDataQB2C)
      let pDataB2C = await elk_excute("/shemaroo_user_plans/_search",pDataQB2C)
      let sum_cDataB2C = 0;
      
      cDataB2C.aggregations.group_by_payment_gateway.buckets.forEach(e => {
        let pg = payment_gateway_india_map[e.key].PG
        let gst = payment_gateway_india_map[e.key].GST

        let price_charged = e.total_price_charged.value
        
        let pg_data = (price_charged * pg) / 100
        //pg_data=price_charged-pg_data
        let gst_data = gst > 0 ? (price_charged - price_charged / 1.18) : 0
        let final_cut=pg_data+gst_data
        final_price = price_charged - final_cut
        sum_cDataB2C += final_price
      });

      let sum_pDataB2C = 0;
      pDataB2C.aggregations.group_by_payment_gateway.buckets.forEach(e => {
        let pg = payment_gateway_india_map[e.key].PG
        let gst = payment_gateway_india_map[e.key].GST

        let price_charged = e.total_price_charged.value
        
        let pg_data = (price_charged * pg) / 100
        //pg_data=price_charged-pg_data
        let gst_data = gst > 0 ? (price_charged - price_charged / 1.18) : 0
        let final_cut=pg_data+gst_data
        final_price = price_charged - final_cut
        sum_pDataB2C += final_price
      });

      let percentageB2C = ((sum_cDataB2C - sum_pDataB2C) / sum_pDataB2C) * 100;

      // let revenue = await QueryExecute(`select round(sum(toFloat64(india_net_revenue)),0) AS revenue from net_revenue where toDate(date) between '${startDate}' and '${currentDate}'`)

      // let prevenue = await QueryExecute(`select round(sum(toFloat64(india_net_revenue)),0) AS prevenue from net_revenue where toDate(date) between '${startDate}' and '${prevDate}'`)
      // //let percentage = ((revenue - prevenue) / prevenue) * 100;

      // let percentage = ((revenue[0].revenue - prevenue[0].prevenue) / prevenue[0].prevenue) * 100;

      let total_cData = sum_cDataB2C + revenueB2B

      let total_pData = sum_pDataB2C + prevenueB2B

      let total_percent = ((total_cData - total_pData) / total_pData) * 100

      res.json({
        success: true,
        data: {
          chart_name: "Revenue",
          chart_data: {
            "b2c": [{
              chart_name: "B2C",
              percentage: percentageB2C, //percentageB2C, //percentage
              chart_data: sum_cDataB2C //sum_cDataB2C //revenue[0].revenue
            }],
            "b2b": [{
              chart_name: "B2B",
              percentage: percentageB2B,
              chart_data: revenueB2B
            }],
            "total": [{
              chart_name: "Total",
              percentage: total_percent,//(percentageB2B + percentageB2C) / 2,
              chart_data: revenueB2B + sum_cDataB2C//sum_cDataB2C //revenue[0].revenue
            }],
           
          },
        },
      });
  
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong " + ex });
  }
};
