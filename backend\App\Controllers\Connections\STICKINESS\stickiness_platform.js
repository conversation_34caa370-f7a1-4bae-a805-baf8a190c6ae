const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");

exports.stickiness_platform = async (req, res) => {
  try {
    const { currentDate, startDate, pstartDate, pcurrentDate, utcpstartDate, utcpcurrentDate } = req.body;

    let {
      formattedCurrentDate,
      formattedPreviousDateStart,
      formattedPreviousPeriodStart,
    } = utc_adjust(startDate, currentDate);

    let cDataDauQ = `
        WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios') AND location_properties_location_country='India' AND event_name='apx_app_opened' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' AND event_name='Stream_Start' THEN custom_properties_device_id END)
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'web' AND location_properties_location_country = 'India' AND event_name = 'APX_PAGE_OPENED' THEN user_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${startDate}T00:00:00' and '${currentDate}T23:59:59'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS dau
      FROM daily_data
      `;

      let pDataDauQ = `
      WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios') AND location_properties_location_country='India' AND event_name='apx_app_opened' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' AND event_name='Stream_Start' THEN custom_properties_device_id END)
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'web' AND location_properties_location_country = 'India' AND event_name = 'APX_PAGE_OPENED' THEN user_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${pstartDate}T00:00:00' and '${pcurrentDate}T23:59:59'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS dau
      FROM daily_data
    `;

    let cDataMauQ = `
    SELECT    
        android + ios + web + external AS mau
    FROM
    (
    SELECT
        countDistinct(IF((device_properties_device_platform = 'android') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS android,
        countDistinct(IF((device_properties_device_platform = 'ios') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS ios,
        countDistinct(IF((device_properties_device_platform = 'web') AND (event_name = 'APX_PAGE_OPENED') AND (location_properties_location_country = 'India'), user_id, NULL)) AS web,
        countDistinct(IF((device_properties_device_platform = 'external') AND (event_name = 'Stream_Start') AND (custom_properties_country = 'IN'), custom_properties_device_id, NULL)) AS external
    FROM apxor
    WHERE ((event_name = 'apx_app_opened' AND device_properties_device_platform IN ('android', 'ios')) 
        OR (event_name = 'APX_PAGE_OPENED' AND device_properties_device_platform = 'web') 
        OR (event_name = 'Stream_Start' AND device_properties_device_platform = 'external'))
        AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${formattedPreviousDateStart} 18:30:00') 
        AND (parseDateTimeBestEffortOrNull(timestamp) <= '${formattedCurrentDate} 18:30:00'))
        ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
      ) AS counts
    `;
    
    let pDataMauQ = `
    SELECT    
        android + ios + web + external AS mau
    FROM
    (
    SELECT
        countDistinct(IF((device_properties_device_platform = 'android') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS android,
        countDistinct(IF((device_properties_device_platform = 'ios') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS ios,
        countDistinct(IF((device_properties_device_platform = 'web') AND (event_name = 'APX_PAGE_OPENED') AND (location_properties_location_country = 'India'), user_id, NULL)) AS web,
        countDistinct(IF((device_properties_device_platform = 'external') AND (event_name = 'Stream_Start') AND (custom_properties_country = 'IN'), custom_properties_device_id, NULL)) AS external
    FROM apxor
    WHERE ((event_name = 'apx_app_opened' AND device_properties_device_platform IN ('android', 'ios')) 
        OR (event_name = 'APX_PAGE_OPENED' AND device_properties_device_platform = 'web') 
        OR (event_name = 'Stream_Start' AND device_properties_device_platform = 'external'))
        AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${utcpstartDate} 18:30:00') 
        AND (parseDateTimeBestEffortOrNull(timestamp) <= '${utcpcurrentDate} 18:30:00'))
        ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
      ) AS counts
    `;

    Promise.all([QueryExecute(cDataDauQ), QueryExecute(cDataMauQ), QueryExecute(pDataDauQ), QueryExecute(pDataMauQ)]).then(
      (results) => {
        const cDataDau = results[0];
        const cDataMau = results[1];
        const pDataDau = results[2];
        const pDataMau = results[3];
        let stick_cData_percent = (cDataDau[0].dau / cDataMau[0].mau) * 100;

        let stick_cData = (cDataDau[0].dau / cDataMau[0].mau);

        let stick_pData = (pDataDau[0].dau / pDataMau[0].mau);

      //   let dau_percent = 
      // ((cDataDau[0].dau - pDataDau[0].dau) / ( pDataDau[0].dau));

      // let mau_percent = ((cDataMau[0].mau - pDataMau[0].mau) / ( pDataMau[0].mau));

      // let percentage = (dau_percent / mau_percent) * 100

      let percentage = ((stick_cData - stick_pData) / stick_pData) * 100;

        res.json({
          success: true,
          data: {
            chart_name: "Stickiness Platform",
            percentage: percentage,
            chart_data: stick_cData_percent,
          },
        });
      }
    );
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong" });
  }
};
