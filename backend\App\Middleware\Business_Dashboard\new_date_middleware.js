function isFullMonthRange(startDateStr, endDateStr) {
  const start = new Date(startDateStr);
  const end = new Date(endDateStr);
  return (
    start.getDate() === 1 &&
    new Date(end.getFullYear(), end.getMonth() + 1, 0).getDate() === end.getDate()
  );
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

function getMonthDiff(start, end) {
  return (
    (end.getFullYear() - start.getFullYear()) * 12 +
    end.getMonth() - start.getMonth()
  );
}

function shiftDateByMonths(original, monthsBack) {
  const year = original.getFullYear();
  const month = original.getMonth() - monthsBack;
  const day = original.getDate();

  const temp = new Date(year, month, 1);
  const maxDay = new Date(temp.getFullYear(), temp.getMonth() + 1, 0).getDate();

  return new Date(temp.getFullYear(), temp.getMonth(), Math.min(day, maxDay));
}

function subtractDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
}

function adjustDates({ start_date, end_date }) {
  const startDate = new Date(start_date);
  const currentDate = new Date(end_date);
  const prevDate = subtractDays(currentDate, 1);

  const is31DayEdgeCase =
    startDate.getDate() === 31 &&
    new Date(startDate.getFullYear(), startDate.getMonth(), 0).getDate() < 31;

  let pstartDate, pcurrentDate;

  if (isFullMonthRange(startDate, currentDate)) {
    const monthDiff = getMonthDiff(startDate, currentDate) + 1;
    pstartDate = new Date(startDate.getFullYear(), startDate.getMonth() - monthDiff, 1);
    pcurrentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - monthDiff + 1, 0);
  } else {
    const monthDiff = Math.max(1, getMonthDiff(startDate, currentDate));
    const sameMonthYear =
      startDate.getFullYear() === currentDate.getFullYear() &&
      startDate.getMonth() === currentDate.getMonth();

    const oneMonthApart =
      (currentDate.getFullYear() === startDate.getFullYear() &&
        currentDate.getMonth() === startDate.getMonth() + 1) ||
      (currentDate.getFullYear() === startDate.getFullYear() + 1 &&
        startDate.getMonth() === 11 && currentDate.getMonth() === 0);

    if (sameMonthYear || oneMonthApart) {
      if (is31DayEdgeCase) {
        pstartDate = shiftDateByMonths(startDate, monthDiff);
        pcurrentDate = new Date(startDate);
      } else {
        pstartDate = shiftDateByMonths(startDate, 1);
        pcurrentDate = shiftDateByMonths(currentDate, 1);
      }
    } else {
      pstartDate = shiftDateByMonths(startDate, monthDiff);
      pcurrentDate = new Date(startDate);
    }
  }

  // UTC-shifted dates
  const utcpstartDate = formatDate(subtractDays(pstartDate, 1)); // Shifted by 1 day
  const utcpcurrentDate = formatDate(pcurrentDate);              // Same due to inclusivity

  return {
    currentDate: formatDate(currentDate),
    startDate: formatDate(startDate),
    prevDate: formatDate(prevDate),
    pstartDate: formatDate(pstartDate),
    pcurrentDate: formatDate(pcurrentDate),
    utcpstartDate,
    utcpcurrentDate,
  };
}

exports.date_middleware = (req, res, next) => {
  try {
    // Fallback to MTD if start_date or end_date is missing
    const today = new Date();
    const defaultStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const defaultEnd = today;

    const start_date = req.body.start_date || defaultStart.toISOString().slice(0, 10);
    const end_date = req.body.end_date || defaultEnd.toISOString().slice(0, 10);

    const result = adjustDates({ start_date, end_date });
    console.log(result);
    req.body = { ...req.body, ...result};

    console.log("Request Body", req.body);
    next();
  } catch (Ex) {
    res.json({ success: false, message: "Invalid date filter " + Ex });
  }
};
