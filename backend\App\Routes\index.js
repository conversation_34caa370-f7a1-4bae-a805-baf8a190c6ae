const express = require("express");
const { keycloakinit } = require("../Middleware/keycloak_middleware");
const { Dau_Controller } = require("../Controllers/Connections/DAU");
const { Mou_Controller } = require("../Controllers/Connections/MOU");
// const {
//   date_middleware
// } = require("../Middleware/Business_Dashboard/date_middlware");
const { date_middleware } = require("../Middleware/Business_Dashboard/new_date_middleware");
const { Mau_Controller } = require("../Controllers/Connections/MAU");
const { Mpu_Controller } = require("../Controllers/Connections/MPU");
const {
  Stickiness_Controller,
} = require("../Controllers/Connections/STICKINESS");
const { Movies_controller } = require("../Controllers/Connections/Top10Videos");
const { Ott_Controller } = require("../Controllers/Connections/Ott");
const { Other_Controller } = require("../Controllers/Connections/Others");
const { Acquisition_Controller } = require("../Controllers/Connections/Acquistion");
const { Category_Controller } = require("../Controllers/Connections/Contribution");
const { plb } = require("../Controllers/Connections/PLB/plb");
const { organic_Controller } = require("../Controllers/Connections/Organic_Innorganic");
const { plb_new } = require("../Controllers/Connections/PLB/plb_new");
const { ftw } = require("../Controllers/Connections/subs_first_watch/ftw");
const { geography } = require("../Controllers/Connections/geography/geo");
const { geo } = require("../Controllers/Connections/geography");
const { hourly_controller } = require("../Controllers/Connections/Hourly");
const { cacheMiddleware } = require("../Middleware/Redis/redis");
const { formatResponse } = require("../Middleware/response_middlware");
const { authenticateToken } = require("../Middleware/authMiddleware");
const { Filters_Controller } = require("../Controllers/Connections/Filters");

const router = express.Router();

//const keycloak = keycloakinit(router);
// router.use(keycloak.protect());

// console.log("Keycloak object from index.js", keycloak)

// router.use(authenticateToken)

router.post("/dau", [date_middleware,cacheMiddleware], Dau_Controller.dau);
router.post("/paid_dau", [date_middleware,cacheMiddleware], Dau_Controller.paid_dau);
router.post("/streaming_dau", [date_middleware,cacheMiddleware], Dau_Controller.streaming_dau);

//mau
router.post("/mau", [date_middleware,cacheMiddleware], Mau_Controller.mau);
router.post("/paid_mau", [date_middleware,cacheMiddleware], Mau_Controller.paid_mau);
router.post("/streaming_mau", [date_middleware,cacheMiddleware], Mau_Controller.streaming_mau);

//mou
router.post("/total_mou", [date_middleware,cacheMiddleware], Mou_Controller.total_mou);
router.post("/paid_mou", [date_middleware,cacheMiddleware], Mou_Controller.paid_mou);
//mpu
router.post("/total_mpu", [date_middleware,cacheMiddleware], Mpu_Controller.total_mpu);
router.post("/paid_mpu", [date_middleware,cacheMiddleware], Mpu_Controller.paid_mpu);

//stickiness
router.post(
  "/stickiness_platform",
  [date_middleware,cacheMiddleware],
  Stickiness_Controller.stickiness_platform
);
router.post(
  "/stickiness_stream",
  [date_middleware,cacheMiddleware],
  Stickiness_Controller.stickiness_stream
);
router.post(
  "/stickiness_paid",
  [date_middleware,cacheMiddleware],
  Stickiness_Controller.stickiness_paid
);

//Top videos
router.post("/top_movies", [date_middleware,cacheMiddleware], Movies_controller.top_10);

//ott
router.post("/revenue", [date_middleware,cacheMiddleware], Ott_Controller.revenue);
router.post("/subscription", [date_middleware,cacheMiddleware], Ott_Controller.Subscription);
router.post("/arpu", [date_middleware,cacheMiddleware], Ott_Controller.arpu);

//pack wise contribution
router.post("/pack_wise_contribution", [date_middleware,cacheMiddleware], Ott_Controller.packwise);

//Others
router.post("/cps", [date_middleware,cacheMiddleware], Other_Controller.cps)
router.post("/total_spends", [date_middleware,cacheMiddleware], Other_Controller.total_spends)
router.post("/performance_spends", [date_middleware,cacheMiddleware], Other_Controller.perfomance_spends)
router.post("/brand_spends", [date_middleware,cacheMiddleware], Other_Controller.brand_spends)
router.post("/roas", [date_middleware,cacheMiddleware], Other_Controller.roas)

// category dashboard spends data

router.post("/total_spends_category", [date_middleware,cacheMiddleware], Filters_Controller.total_spends_category);
router.post("/performance_spends_category", [date_middleware,cacheMiddleware], Filters_Controller.performance_spends_category);
router.post("/brand_spends_category", [date_middleware,cacheMiddleware], Filters_Controller.brand_spends_category)

// category dashboard acquistion dashboard data

router.post("/acq_category", [date_middleware, cacheMiddleware], Filters_Controller.acquisition_dashboard_category);

//filters 2nd page
router.post("/first_watch_analysis", [date_middleware, cacheMiddleware],Filters_Controller.first_watch_analysis)
router.post("/completion_rate", [date_middleware, cacheMiddleware], Filters_Controller.completion_rate)
router.post("/who_watched", [date_middleware, cacheMiddleware], Filters_Controller.who_watched);
router.post("/fwa_subscriptions", [date_middleware, cacheMiddleware], Filters_Controller.fwa_subscriptions);
router.post("/content_performance", [date_middleware, cacheMiddleware], Filters_Controller.content_performance);

//Acquisition
router.post("/acquistion", [date_middleware,cacheMiddleware], Acquisition_Controller.acquistion);

//Contribution
router.post("/category_contrib", [date_middleware,cacheMiddleware], Category_Controller.category_contrib)
router.post("/platform_contrib", [date_middleware,cacheMiddleware], Category_Controller.platform_contrib)
router.post("/category_contrubution",[date_middleware,cacheMiddleware],Category_Controller.category_contribution)


//auto debit mandate and plbs
router.post("/autodebit",[date_middleware,cacheMiddleware],Other_Controller.autoDebit)
// router.post("/plb",[date_middleware,cacheMiddleware],plb)
router.post("/plb",[date_middleware,cacheMiddleware],plb_new)
//active user analyis

router.post("/stream_and_did_not_stream",[date_middleware,cacheMiddleware],Other_Controller.stream_did_not)
router.post("/install_unistall",[date_middleware,cacheMiddleware],Other_Controller.install_unistall)
router.post("/active_user_analysis",[date_middleware,cacheMiddleware],Other_Controller.active_user_analysis)

//3rd page
router.post('/revenue_trend',[date_middleware,cacheMiddleware],Other_Controller.revenue_trend)
router.post('/title_contribution',[date_middleware,cacheMiddleware],Other_Controller.title_contrubution)
//organic and innorganic Events
router.post('/subs_arpu_cps',[date_middleware,cacheMiddleware],Other_Controller.subs_arpu_cps)

router.post('/inorganic',[date_middleware,cacheMiddleware],organic_Controller.inorganic)
router.post('/organic',[date_middleware,cacheMiddleware],organic_Controller.organic)

//4th page
//top titles filtered
router.post('/top_titles',[date_middleware,cacheMiddleware],Other_Controller.top_titles)
router.post('/ftw',[date_middleware,cacheMiddleware],ftw)
router.post('/geo',[date_middleware,cacheMiddleware],geography)
router.post('/geo_per_state',[date_middleware,cacheMiddleware],geo.geo_per_state)
router.post('/auto_renewal', [date_middleware,cacheMiddleware], Other_Controller.autoRenewal)
router.post('/device_contribution', [date_middleware,cacheMiddleware], Other_Controller.deviceContrib)
router.post('/packwise_monthwise', [date_middleware,cacheMiddleware], Ott_Controller.packwise_monthwise)
router.post('/hourly_consumption',[date_middleware,cacheMiddleware],Other_Controller.h_data)

//content type analysis
router.post('/content_type_analysis', [date_middleware,cacheMiddleware], Other_Controller.contentTypeAnalysis)


//5th page
router.post('/hourly_trend',[date_middleware,cacheMiddleware],hourly_controller.hourly_trend)
router.post('/paid_user_contribution', [date_middleware,cacheMiddleware], Other_Controller.paidUserContribution)
router.post('/country', [date_middleware,cacheMiddleware],geo.country_transaction )
router.post('/city', [date_middleware,cacheMiddleware],geo.city_transaction )

//Marketing Team API Revenue


router.post('/b2b_b2c_market', [date_middleware,cacheMiddleware], Ott_Controller.marketing_revenue);
// router.post('/inorganic_net_revenue', [date_middleware,cacheMiddleware], organic_Controller.inorganic_net_revenue);
router.post("/organic_net_revenue", [date_middleware],organic_Controller.organic_net_revenue);
router.post("/autorenewal_net_revenue", [date_middleware],organic_Controller.auto_net_revenue);
router.post("/top_10_gujarati", [date_middleware, cacheMiddleware], Movies_controller.top_10_gujarati);
// router.post('/ott_lake_inorganic_net_revenue', [date_middleware,cacheMiddleware], organic_Controller.ott_lake_inorganic_net_revenue);
router.post('/inorganic_net_revenue', [date_middleware,cacheMiddleware], organic_Controller.ott_lake_inorganic_net_revenue);
router.post("/active_user", [date_middleware], Other_Controller.active_user_analysis_updated);

router.post("/test_autorenewal", [date_middleware,cacheMiddleware], organic_Controller.auto_net_revenue);
module.exports = router;
