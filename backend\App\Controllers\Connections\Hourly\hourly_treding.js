const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const moment = require("moment-timezone");
const { payment_gateway_india_map, payment_gateway_row_map } = require("../Ott/payment_gateway_map");
const { getAggregationsROW, processDataROW } = require("./row_hourly_trending");

const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];

const b2c_payment_gateways_row = [
  "apple_store",
  "google",
  "coupon",
  "google_play_tv",
  "stripe",
  "adyen",
];

const addTimeOffset = (dateStr, hours, minutes) => {
  const date = new Date(dateStr);
  date.setHours(date.getHours() + hours);
  date.setMinutes(date.getMinutes() + minutes);
  return date.toISOString();
};
const getAggregations = async (startDate, endDate, filter_type, type) => {
  let t = filter_type != 'IN' 
    ? {
        "bool": {
          "must_not": [
            {
              "terms": {
                "region.keyword": ["IN", "US", "CA"]
              }
            }
          ]
        }
      }
    : {
        match: { "region.keyword": "IN" }
      };

  return await elk_excute("/shemaroo_user_plans/_search", {
    query: {
      bool: {
        must: [
          {
            range: {
              created_at: {
                gte: startDate,
                lt: endDate,
              },
            },
          },
          { terms: { "payment_gateway.keyword": filter_type != 'IN' ? b2c_payment_gateways_row : b2c_payment_gateways } },
          {
            terms: {
              "txn_status.keyword": type === "new" ? [
                "success",
                //"autorenewal_success",
                "upgrade",
                "renewal",
                "autorenew",
              ] : type === "autorenewal" ? ["autorenewal_success"] : [
                "success",
                "autorenewal_success",
                "upgrade",
                "renewal",
                "autorenew",
              ],
            },
          },
          t,
          { match: { "transaction_env.keyword": "production" } },
          {
            terms: {
              "period.keyword": [
                "Monthly",
                "Yearly",
                "Quarterly",
                "Two Yearly",
                "Half Yearly",
              ],
            },
          },
        ],
      },
    },
    aggs: {
      hourly_data: {
        date_histogram: { 
          field: "created_at", 
          // calendar_interval: "hour"
          fixed_interval: "1h",
          "time_zone": "Asia/Kolkata",
          "min_doc_count": 0,
          "extended_bounds": {
                "min": startDate,
                "max": endDate
            }
        },
        aggs: {
          periods: {
            terms: { field: "period.keyword" },
            aggs: {
              payment_gateways: {
                terms: { field: "payment_gateway.keyword" },
                aggs: {
                  total_price_charged: { sum: { field: "price_charged" } }
                }
              }
            }
          },
        },
      },
      daily_total: { sum: { field: "price_charged" } },
    },
    size: 0,
  });
};

const processData = (data, filter_type) => {
  let result = { daywise: {} };
  
  data.aggregations.hourly_data.buckets.forEach((hourlyBucket) => {
    hourlyBucket.key_as_string = moment(hourlyBucket.key_as_string)
      .add(6, 'hours')
      .format('YYYY-MM-DDTHH:mm:ss[Z]');

    // Initialize hourly data for every time slot, even if empty
    if (!result["daywise"][hourlyBucket.key_as_string]) {
      result["daywise"][hourlyBucket.key_as_string] = {
        transactions: 0,
        revenue: 0,
        consolidated: [],
      };
    }

    // Skip period processing if no periods, but keep the time slot
    if (!hourlyBucket.periods.buckets.length) return;

    hourlyBucket.periods.buckets.forEach((periodBucket) => {
      // Initialize period data if not exists
      if (!result[periodBucket.key]) {
        result[periodBucket.key] = { transactions: 0, revenue: 0 };
      }

      // Skip this period if it has no payment gateways
      if (!periodBucket.payment_gateways.buckets.length) return;

      let periodNetRevenue = 0;
      // Calculate net revenue for each payment gateway
      periodBucket.payment_gateways.buckets.forEach((pgBucket) => {
        const pg = filter_type != 'IN' ? payment_gateway_row_map[pgBucket.key].PG : payment_gateway_india_map[pgBucket.key].PG;
        const gst = filter_type != 'IN' ? payment_gateway_row_map[pgBucket.key].GST : payment_gateway_india_map[pgBucket.key].GST;
        
        const grossAmount = pgBucket.total_price_charged.value;
        const pgAmount = (grossAmount * pg) / 100;
        const gstAmount = gst > 0 ? (grossAmount - grossAmount / 1.18) : 0;
        const netAmount = grossAmount - (pgAmount + gstAmount);
        
        periodNetRevenue += netAmount;
      });

      // Update period totals
      result[periodBucket.key].transactions += periodBucket.doc_count || 0;
      result[periodBucket.key].revenue += periodNetRevenue;

      // Initialize hourly data if not exists
      // if (!result["daywise"][hourlyBucket.key_as_string]) {
      //   result["daywise"][hourlyBucket.key_as_string] = {
      //     transactions: 0,
      //     revenue: 0,
      //     consolidated: [],
      //   };
      // }

      // Update hourly totals
      result["daywise"][hourlyBucket.key_as_string].transactions += periodBucket.doc_count || 0;
      result["daywise"][hourlyBucket.key_as_string].revenue += periodNetRevenue;
      
      // Add consolidated data
      result["daywise"][hourlyBucket.key_as_string]["consolidated"].push({
        transactions: periodBucket.doc_count,
        key: periodBucket.key,
        revenue: periodNetRevenue,
      });
    });
  });
  
  return result;
};

const calculatePercentages = (current, previous) => {
  const percentages = {};
  for (const [key, value] of Object.entries(current)) {
    if (previous[key]) {
      percentages[key] = {
        transactions: previous[key].transactions
          ? ((value.transactions - previous[key].transactions) /
              previous[key].transactions) *
            100
          : 0,
        subs: previous[key].subs
          ? ((value.subs - previous[key].subs) / previous[key].subs) * 100
          : 0,
      };
    } else {
      percentages[key] = { transactions: 100, subs: 100 }; // New period compared to non-existing previous data
    }
  }
  return percentages;
};

exports.hourly_trend = async (req, res) => {
  try {
    // const now = moment().subtract(5, 'hours').subtract(30, 'minutes').tz('Asia/Kolkata');;

    // const todayStart = now.startOf("day").format('YYYY-MM-DDTHH:mm:ssZ');
    // // console.log(now)
    // const todayEnd = now.endOf("day").format('YYYY-MM-DDTHH:mm:ssZ');

    // const yesterdayStart = now.subtract(1, "days").startOf("day").format('YYYY-MM-DDTHH:mm:ssZ');
    // const yesterdayEnd = now.endOf("day").format('YYYY-MM-DDTHH:mm:ssZ');

    // const lastWeekStart = now.subtract(7, "days").startOf("day").format('YYYY-MM-DDTHH:mm:ssZ');
    // const lastWeekEnd = now.endOf("day").format('YYYY-MM-DDTHH:mm:ssZ');

    const now = moment().utc();

    const todayStart = now.clone().subtract(1, "days").startOf("day").format('YYYY-MM-DD[T18:30:00Z]');
    // console.log(now)
    const todayEnd = now.clone().format('YYYY-MM-DD[T18:29:59Z]');

    const yesterdayStart = now.clone().subtract(2, "days").startOf("day").format('YYYY-MM-DD[T18:30:00Z]');
    const yesterdayEnd = now.clone().subtract(1, "days").endOf("day").format('YYYY-MM-DD[T18:29:59Z]');

    const lastWeekStart = now.clone().subtract(8, "days").startOf("day").format('YYYY-MM-DD[T18:30:00Z]');
    const lastWeekEnd = now.clone().subtract(7, "days").endOf("day").format('YYYY-MM-DD[T18:29:59Z]');

    console.log("Today: ", todayStart,todayEnd,now)
    console.log("Yesterday: ", yesterdayStart,yesterdayEnd)
    console.log("Last Week: ", lastWeekStart,lastWeekEnd)

    const {filter_type} =req.body

    let todayData, yesterdayData, lastWeekData;

    let todayResult, yesterdayResult, lastWeekResult;

    let percentageYesterday, percentageLastWeek;

    if(filter_type === 'IN'){
      [todayData, yesterdayData, lastWeekData] = await Promise.all([
        getAggregations(todayStart, todayEnd,filter_type, req.body.type),
        getAggregations(yesterdayStart, yesterdayEnd,filter_type, req.body.type),
        getAggregations(lastWeekStart, lastWeekEnd,filter_type, req.body.type),
      ]);

      todayResult = processData(todayData, req.body.filter_type);
      yesterdayResult = processData(yesterdayData, req.body.filter_type);
      lastWeekResult = processData(lastWeekData, req.body.filter_type);

      percentageYesterday = calculatePercentages(
        todayResult,
        yesterdayResult
      );
      percentageLastWeek = calculatePercentages(
        todayResult,
        lastWeekResult
      );
    } else {
      [todayData, yesterdayData, lastWeekData] = await Promise.all([
        getAggregationsROW(todayStart, todayEnd, req.body.type, req.body.region),
        getAggregationsROW(yesterdayStart, yesterdayEnd, req.body.type, req.body.region),
        getAggregationsROW(lastWeekStart, lastWeekEnd, req.body.type, req.body.region),
      ]);

      todayResult = processDataROW(todayData);
      yesterdayResult = processDataROW(yesterdayData);
      lastWeekResult = processDataROW(lastWeekData);

      percentageYesterday = calculatePercentages(
        todayResult,
        yesterdayResult
      );
      percentageLastWeek = calculatePercentages(
        todayResult,
        lastWeekResult
      );
    }

    // let s1 = {};
    // Object.keys(todayResult).forEach((period) => {
    //   s1[period] = {
    //     transactions: todayResult[period].transactions,
    //     subs: todayResult[period].subs,
    //   };
    // });

    res.json({
      success: true,
      "region": req.body.region || filter_type,
      todayResult,
      yesterdayResult,
      lastWeekResult,
      percentageYesterday,
      percentageLastWeek,
    });
  } catch (ex) {
    res.json({ error: ex.message });
  }
};
