const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");

exports.stickiness_stream = (req, res) => {
  const { currentDate, startDate, pstartDate, pcurrentDate, utcpstartDate, utcpcurrentDate } = req.body;

  // Validate dates or handle any date parsing errors if necessary

  let {
    formattedCurrentDate,
    formattedPreviousDateStart,
    formattedPreviousPeriodStart,
  } = utc_adjust(startDate, currentDate);

  // Parameterized queries to prevent SQL injection
  const dauMauQuery = `
    WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios', 'web') AND location_properties_location_country='India' AND event_name='Stream_End' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' AND event_name='Stream_End' THEN custom_properties_device_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${startDate}T00:00:00' and '${currentDate}T23:59:59'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS streaming_dau
      FROM daily_data`;

    const pdauMauQuery = `
    WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios', 'web') AND location_properties_location_country='India' AND event_name='Stream_End' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' AND event_name='Stream_End' THEN custom_properties_device_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${pstartDate}T00:00:00' and '${pcurrentDate}T23:59:59'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS streaming_dau
      FROM daily_data`;

  const mauQuery = `
    SELECT
    android + ios + web + external AS streaming_mau
    FROM
    (
        SELECT
            countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
            )) AS android,        
            countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
            )) AS ios,        
            countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
            )) AS web,        
            countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                custom_properties_device_id, NULL)) AS external        
        FROM apxor
        WHERE
            event_name = 'Stream_End'
            AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${formattedPreviousDateStart} 18:30:00') 
            AND (parseDateTimeBestEffortOrNull(timestamp) <= '${formattedCurrentDate} 18:30:00'))
            ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
    ) AS counts
    `;

    const pmauQuery = `
    SELECT
    android + ios + web + external AS streaming_mau
    FROM
    (
        SELECT
            countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
            )) AS android,        
            countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
            )) AS ios,        
            countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
            )) AS web,        
            countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                custom_properties_device_id, NULL)) AS external        
        FROM apxor
        WHERE
            event_name = 'Stream_End'
            AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${utcpstartDate} 18:30:00') 
            AND (parseDateTimeBestEffortOrNull(timestamp) <= '${utcpcurrentDate} 18:30:00'))
            ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
    ) AS counts
    `;

  // Execute the queries with parameters
  Promise.all([
    QueryExecute(dauMauQuery, [startDate, currentDate]),
    QueryExecute(mauQuery, [startDate, currentDate]),
    QueryExecute(pdauMauQuery, [startDate, currentDate]),
    QueryExecute(pmauQuery, [startDate, currentDate])
  ])
  .then(results => {
    const cDataDauStream = results[0];
    const cDataMauStream = results[1];
    const pDataDauStream = results[2];
    const pDataMauStream = results[3];

    // let dau_percent = 
    //   ((cDataDauStream[0].streaming_dau - pDataDauStream[0].streaming_dau) / ( pDataDauStream[0].streaming_dau));

    //   let mau_percent = ((cDataMauStream[0].streaming_mau - pDataMauStream[0].streaming_mau) / ( pDataMauStream[0].streaming_mau));

    //   let percentage = (dau_percent / mau_percent) * 100

    // Calculate stickiness
    const stick_cData_percent = (cDataDauStream[0].streaming_dau / cDataMauStream[0].streaming_mau) * 100;

    let stick_cData = (cDataDauStream[0].streaming_dau / cDataMauStream[0].streaming_mau)

    let stick_pData = (pDataDauStream[0].streaming_dau / pDataMauStream[0].streaming_mau)

    let percentage = ((stick_cData - stick_pData) / stick_pData) * 100

    // Return the result as JSON response
    res.json({
      success: true,
      data: {
        chart_name: "Stickiness Streaming",
        percentage: percentage,
        chart_data: stick_cData_percent,
      },
    });
  })
  .catch(error => {
    console.error("Error in stickiness_stream:", error);
    res.status(500).json({ success: false, error: "Internal server error" });
  });
};
