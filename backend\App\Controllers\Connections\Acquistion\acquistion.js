const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");

const chartEnabled = async (startDate, currentDate) => {
//   let non_g_query = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Performance Non Gujarati' AS Type,
//       round(sumIf(value, type = 'Edgy Revenue')) AS revenue,
//       round(sumIf(value, type = 'Edgy Spends')) AS spends,
//       round(sumIf(value, type = 'Edgy Perf Subs')) AS subs,
//       round(sumIf(value, type = 'Edgy Perf CPS')) AS cps,
//       round(sumIf(value, type = 'Edgy ARPU')) AS arpu
//     FROM cps_ytd
//     WHERE type IN ('Edgy Revenue', 'Edgy Spends', 'Edgy Perf Subs', 'Edgy Perf CPS', 'Edgy ARPU')
//       AND time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

//   let g_query = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Performance Gujarati' AS Type,
//       round(sumIf(value, type = 'Guj Revenue')) AS revenue,
//       round(sumIf(value, type = 'Guj Spends')) AS spends,
//       round(sumIf(value, type = 'Guj Perf Subs')) AS subs,
//       round(sumIf(value, type = 'Guj Perf CPS')) AS cps,
//       round(sumIf(value, type = 'Guj ARPU')) AS arpu
//     FROM cps_ytd
//     WHERE type IN ('Guj Revenue', 'Guj Spends', 'Guj Perf Subs', 'Guj Perf CPS', 'Guj ARPU')
//       AND time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

//   let aff_query = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Affiliate' AS Type,
//       '0' AS revenue,
//       '0' AS spends,
//       '0' AS subs,
//       '0' AS cps,
//       '0' AS arpu
//     FROM cps_ytd
//     WHERE time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

//   let total_perf = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Total Performance' AS Type,
//       round(sumIf(value, type = 'Perf. Revenue')) AS revenue,
//       round(sumIf(value, type = 'Total Perf Spends')) AS spends,
//       round(sumIf(value, type = 'Total Perf Subs')) AS subs,
//       round(sumIf(value, type = 'Perf CPS')) AS cps,
//       round(sumIf(value, type = 'Perf. ARPU')) AS arpu
//     FROM cps_ytd
//     WHERE type IN ('Perf. Revenue', 'Total Perf Spends', 'Total Perf Subs', 'Perf CPS', 'Perf. ARPU')
//       AND time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

//   let auto_renewals = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Auto Renewals' AS Type,
//       round(sumIf(value, type = 'AR Revenue')) AS revenue,
//       '0' AS spends,
//       round(sumIf(value, type = 'AR Subs')) AS subs,
//       '0' AS cps,
//       round(sumIf(value, type = 'AR ARPU')) AS arpu
//     FROM cps_ytd
//     WHERE type IN ('AR Revenue', 'AR Subs', 'AR ARPU')
//       AND time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

//   let organic = await QueryExecute(`
//     SELECT 
//       toStartOfMonth(toDate(time)) AS month,
//       'Organic' AS Type,
//       round(sumIf(value, type = 'Organic Revenue')) AS revenue,
//       round(sumIf(value, type = 'Organic Spends')) AS spends,
//       round(sumIf(value, type = 'Organic CPS')) AS cps,
//       round(sumIf(value, type = 'Organic Subs')) AS subs,
//       round(sumIf(value, type = 'Organic ARPU')) AS arpu
//     FROM cps_ytd
//     WHERE type IN ('Organic Revenue', 'Organic Spends', 'Organic CPS', 'Organic Subs', 'Organic ARPU')
//       AND time BETWEEN '${startDate}' AND '${currentDate}'
//     GROUP BY month
//     ORDER BY month
// `);

try{
  // let non_g_query = await QueryExecute(`
  //   SELECT 
  //    Date AS month,
  //     'Performance Non Gujarati' AS Type,
  //      Edgy_Net_Revenue AS revenue,
  //      Edgy_Spends AS spends,
  //      Edgy_Subscription AS subs,
  //      Edgy_CPS AS cps,
  //      Edgy_Net_ARPU AS arpu
  //   FROM cpsd where Date between '${startDate}' AND '${currentDate}'
  //    ORDER BY month
  //   `);

    let non_g_query = await QueryExecute(`
        SELECT 
          Date AS month,
          'Performance Non Gujarati' AS Type,
          (toFloat64(Edgy_Net_Revenue) + toFloat64(Affiliate_Net_Revenue)) AS revenue,
          (toFloat64(Edgy_Spends) + toFloat64(Affiliate_Spends)) AS spends,
          (toFloat64(Edgy_Subscription) + toFloat64(Affiliate_Subscription)) AS subs,
          COALESCE(
              (toFloat64(Edgy_Spends) + toFloat64(Affiliate_Spends)) / 
              NULLIF((toFloat64(Edgy_Subscription) + toFloat64(Affiliate_Subscription)), 0), 
              0
          ) AS cps,
          COALESCE(
              (toFloat64(Edgy_Net_Revenue) + toFloat64(Affiliate_Net_Revenue)) / 
              NULLIF((toFloat64(Edgy_Subscription) + toFloat64(Affiliate_Subscription)), 0), 
              0
          ) AS arpu
      FROM cpsd 
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
      ORDER BY month
    `);

let g_query = await QueryExecute(`
  SELECT 
  Date AS month,
 'Performance Gujarati' AS Type,
   Gujarati_Revenue AS revenue,
   Gujarati_Spends AS spends,
   Gujarati_Subscriptions AS subs,
   Gujarati_CPS AS cps,
   Gujarati_ARPU AS arpu
   FROM cpsd
 WHERE  Date between '${startDate}' AND '${currentDate}'

 ORDER BY month
 `);

//  let aff_query = await QueryExecute(`
//  SELECT 
//  Date AS month,
//  'Affiliate' AS Type,
//    Affiliate_Net_Revenue AS revenue,
//    Affiliate_Spends AS spends,
//    Affiliate_Subscription AS subs,
//    Affiliate_CPS AS cps,
//    Affiliate_Net_ARPU AS arpu
//    FROM cpsd  WHERE  Date between '${startDate}' AND '${currentDate}'
  
//   ORDER BY month
//  `);

 let total_perf = await QueryExecute(`
 SELECT 
 Date AS month,
   'Total Performance' AS Type,
   Perf_Revenue AS revenue,
   Total_Perf_Spends AS spends,
  Total_Perf_Subs  AS subs,
   Perf_CPS AS cps,
   Perf_ARPU AS arpu
 FROM cpsd
  WHERE  Date between '${startDate}' AND '${currentDate}'
 
 ORDER BY month
 `);

 let auto_renewals = await QueryExecute(`
 SELECT 
 Date AS month,
 'Auto Renewals' AS Type,
 AR_Revenue AS revenue,
 '0' AS spends,
 AR_Subs AS subs,
 '0' AS cps,
 AR_ARPU AS arpu
    FROM cpsd
  WHERE  Date between '${startDate}' AND '${currentDate}'
   
 ORDER BY month
 `);

 let organic = await QueryExecute(`
 SELECT 
 Date as month,
   'Organic' AS Type,
   Organic_Revenue AS revenue,
   Organic_Spends AS spends,
   Organic_CPS AS cps,
   Organic_Subs AS subs,
   Organic_ARPU AS arpu
   FROM cpsd
   WHERE  Date between '${startDate}' AND '${currentDate}'
 ORDER BY month
 `);


const result_data = [
  ...non_g_query,
  ...g_query,
  // ...aff_query,
  ...total_perf,
  ...auto_renewals,
  ...organic
];


  const renameKeys = (array) => {
    return array.map((item) => ({
      created_at_time: item.month,
      type: item.Type,
      revenue: parseInt(item.revenue),
      spends: parseInt(item.spends.toString()),
      subs: parseInt(item.subs.toString()),
      cps: parseInt(item.cps.toString()),
      arpu: parseInt(item.arpu.toString()),
    }));
  };

  const result = renameKeys(result_data);

  return result;
}catch(ex){
  console.log(ex)
}
};

exports.acquistion = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate } = req.body;
    // const a = [
    //   { type: "Performance Non Gujarati", revenue: "17587", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    //   { type: "Peformance Gujarati", revenue: "19246", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    //   { type: "Affiliate", revenue: "14487", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    //   { type: "Total Performance", revenue: "22752", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    //   { type: "Auto Renewals", revenue: "13005", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    //   { type: "Organic", revenue: "13005", spends: "35484", subs: "1236", cps: "463", arpu: "578" },
    // ];
    // res.json({
    //   success: true,
    //   data: {
    //     chart_name: "Acquisition",
    //     chart_data: a,
    //     daily_wise_data: [
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Performance Non Gujarati",
    //       },
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Performance Gujarati",
    //       },
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Affiliate",
    //       },
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Total Perfromance",
    //       },
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Auto Renewals",
    //       },
    //       {
    //         created_at_time: "2023-06-01",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Organic",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Performance Non Gujarati",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Performance Gujarati",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Affiliate",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Total Perfromance",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Auto Renewals",
    //       },
    //       {
    //         created_at_time: "2023-06-02",
    //         subs: "3074",
    //         revenue: "2140527",
    //         spends: "696",
    //         cps: "364",
    //         type: "Organic",
    //       },
    //     ],
    //   },
    // });


    // let non_g_query = await QueryExecute(`
    // SELECT 
    //   'Performance Non Gujarati' AS Type,
    //   round(sumIf(value, type = 'Edgy Revenue')) AS revenue,
    //   round(sumIf(value, type = 'Edgy Spends')) AS spends,
    //   round(sumIf(value, type = 'Edgy Perf Subs')) AS subs,
    //   round(sumIf(value, type = 'Edgy Perf CPS')) AS cps,
    //   round(sumIf(value, type = 'Edgy ARPU')) AS arpu
    // FROM cpsd
    // WHERE type IN ('Edgy Revenue', 'Edgy Spends', 'Edgy Perf Subs', 'Edgy Perf CPS', 'Edgy ARPU') AND time between '${startDate}' AND '${currentDate}'
    // `);

    // let non_g_query = await QueryExecute(`
    // SELECT 
    //   'Performance Non Gujarati' AS Type,
    //    Edgy_Net_Revenue AS revenue,
    //    Edgy_Spends AS spends,
    //    Edgy_Subscription AS subs,
    //    Edgy_CPS AS cps,
    //    Edgy_Net_ARPU AS arpu
    // FROM cpsd where Date between '${startDate}' AND '${currentDate}'
    // `);
//     let non_g_query = await QueryExecute(`
//       SELECT 
//     'Performance Non Gujarati' AS Type,
//     sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Net_Revenue), '[^0-9.]', ''))) AS revenue,
//     sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Spends), '[^0-9.]', ''))) AS spends,
//     sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))) AS subs,
//     sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Spends), '[^0-9.]', ''))) / NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))), 0) AS cps,
//     NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Net_Revenue), '[^0-9.]', ''))), 0) / NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))), 0) AS arpu
// FROM cpsd
// WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
//   `);

  let non_g_query = await QueryExecute(`
      SELECT 
      'Performance Non Gujarati' AS Type,
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Net_Revenue), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Net_Revenue), '[^0-9.]', ''))) AS revenue,
      
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Spends), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Spends), '[^0-9.]', ''))) AS spends,
      
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', ''))) AS subs,
      
      (sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Spends), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Spends), '[^0-9.]', '')))) / 
      NULLIF((sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', '')))), 0) AS cps,
      
      NULLIF((sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Net_Revenue), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Net_Revenue), '[^0-9.]', '')))), 0) / 
      NULLIF((sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Edgy_Subscription), '[^0-9.]', ''))) + 
      sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', '')))), 0) AS arpu
    FROM cpsd
    WHERE Date BETWEEN '${startDate}' AND '${currentDate}' 
`);
  

    // let g_query = await QueryExecute(`
    // SELECT 
    // 'Performance Gujarati' AS Type,
    //   round(sumIf(value, type = 'Guj Revenue')) AS revenue,
    //   round(sumIf(value, type = 'Guj Spends')) AS spends,
    //   round(sumIf(value, type = 'Guj Perf Subs')) AS subs,
    //   round(sumIf(value, type = 'Guj Perf CPS')) AS cps,
    //   round(sumIf(value, type = 'Guj ARPU')) AS arpu
    //   FROM cps_ytd
    // WHERE type IN ('Guj Revenue', 'Guj Spends', 'Guj Perf Subs', 'Guj Perf CPS', 'Guj ARPU') AND time between '${startDate}' AND '${currentDate}'
    // `);

    //  let g_query = await QueryExecute(`
    //  SELECT 
    // 'Performance Gujarati' AS Type,
    //   Gujarati_Revenue AS revenue,
    //   Gujarati_Spends AS spends,
    //   Gujarati_Subscriptions AS subs,
    //   Gujarati_CPS AS cps,
    //   Gujarati_ARPU AS arpu
    //   FROM cpsd
    // WHERE  Date between '${startDate}' AND '${currentDate}'
    // `);

    // let aff_query = await QueryExecute(`
    // SELECT 
    // 'Affiliate' AS Type,
    //   Affiliate_Net_Revenue AS revenue,
    //   Affiliate_Spends AS spends,
    //   Affiliate_Subscription AS subs,
    //   Affiliate_CPS AS cps,
    //   Affiliate_Net_ARPU AS arpu
    //   FROM cpsd  WHERE  Date between '${startDate}' AND '${currentDate}'
    // `);

    // let total_perf = await QueryExecute(`
    // SELECT 
    //   'Total Performance' AS Type,
    //   Perf_Revenue AS revenue,
    //   Total_Perf_Spends AS spends,
    //  Total_Perf_Subs  AS subs,
    //   Perf_CPS AS cps,
    //   Perf_ARPU AS arpu
    // FROM cpsd
    //  WHERE  Date between '${startDate}' AND '${currentDate}'
    // `);

    // let auto_renewals = await QueryExecute(`
    // SELECT 
    // 'Auto Renewals' AS Type,
    // AR_Revenue AS revenue,
    // '0' AS spends,
    // AR_Subs AS subs,
    // '0' AS cps,
    // AR_ARPU AS arpu
    //    FROM cpsd
    //  WHERE  Date between '${startDate}' AND '${currentDate}'
    // `);

    // let organic = await QueryExecute(`
    // SELECT 
    //   'Organic' AS Type,
    //   Organic_Revenue AS revenue,
    //   Organic_Spends AS spends,
    //   Organic_CPS AS cps,
    //   Organic_Subs AS subs,
    //   Organic_ARPU AS arpu
    //   FROM cpsd
    //   WHERE  Date between '${startDate}' AND '${currentDate}'
    // `);

    let g_query = await QueryExecute(`
      SELECT 
          'Performance Gujarati' AS Type,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Revenue), '[^0-9.]', ''))) AS revenue,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Spends), '[^0-9.]', ''))) AS spends,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Subscriptions), '[^0-9.]', ''))) AS subs,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Spends), '[^0-9.]', ''))) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Subscriptions), '[^0-9.]', ''))), 0) AS cps,
          NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Revenue), '[^0-9.]', ''))), 0) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Gujarati_Subscriptions), '[^0-9.]', ''))), 0) AS arpu
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
  `);
  
  // let aff_query = await QueryExecute(`
  //     SELECT 
  //         'Affiliate' AS Type,
  //         sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Net_Revenue), '[^0-9.]', ''))) AS revenue,
  //         sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Spends), '[^0-9.]', ''))) AS spends,
  //         sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', ''))) AS subs,
  //         sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Spends), '[^0-9.]', ''))) / 
  //             NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', ''))), 0) AS cps,
  //         NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Net_Revenue), '[^0-9.]', ''))), 0) / 
  //             NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Affiliate_Subscription), '[^0-9.]', ''))), 0) AS arpu
  //     FROM cpsd
  //     WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
  // `);
  
  let total_perf = await QueryExecute(`
      SELECT 
          'Total Performance' AS Type,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Perf_Revenue), '[^0-9.]', ''))) AS revenue,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Spends), '[^0-9.]', ''))) AS spends,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Subs), '[^0-9.]', ''))) AS subs,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Spends), '[^0-9.]', ''))) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Subs), '[^0-9.]', ''))), 0) AS cps,
          NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Perf_Revenue), '[^0-9.]', ''))), 0) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Subs), '[^0-9.]', ''))), 0) AS arpu
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
  `);
  
  let auto_renewals = await QueryExecute(`
      SELECT 
          'Auto Renewals' AS Type,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Revenue), '[^0-9.]', ''))) AS revenue,
          sum(0) AS spends,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Subs), '[^0-9.]', ''))) AS subs,
          sum(0) AS cps,
          NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Revenue), '[^0-9.]', ''))), 0) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Subs), '[^0-9.]', ''))), 0) AS arpu
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
  `);
  
  let organic = await QueryExecute(`
      SELECT 
          'Organic' AS Type,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Revenue), '[^0-9.]', ''))) AS revenue,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Spends), '[^0-9.]', ''))) AS spends,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Subs), '[^0-9.]', ''))) AS subs,
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Spends), '[^0-9.]', ''))) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Subs), '[^0-9.]', ''))), 0) AS cps,
          NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Revenue), '[^0-9.]', ''))), 0) / 
              NULLIF(sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Subs), '[^0-9.]', ''))), 0) AS arpu
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
  `);  

    const result_data = [
      non_g_query[0],
      g_query[0],
      // aff_query[0],
      total_perf[0],
      auto_renewals[0],
      organic[0],
    ];

    const renameKeys = (array) => {
      let total_revenue = 0
      let total_spends = 0
      let total_subs = 0
      let total_cps = 0
      let total_arpu = 0


      let n_total_revenue = 0
      let n_total_spends = 0
      let n_total_subs = 0
      let n_total_cps = 0
      let n_total_arpu = 0

      let final_array = array.map((item) => {
        if(item.Type === "Organic" || item.Type === "Auto Renewals" || item.Type === "Total Performance"){
          total_revenue += parseInt(item.revenue)
          total_spends += parseInt(item.spends)
          total_subs += parseInt(item.subs)
          total_cps = total_subs > 0 ? Math.round((total_spends / total_subs)) : 0
          total_arpu = total_subs > 0 ? Math.round((total_revenue / total_subs)) : 0
        }
        
        return {
          type: item.Type,
          revenue: item.revenue.toString(),
          spends: item.spends.toString(),
          subs: item.subs.toString(),
          cps: item.cps.toString(),
          arpu: item.arpu.toString(),
        };
      });

       array.forEach((item) => {
        if(item.Type === "Organic" || item.Type === "Total Performance"){
          n_total_revenue += parseInt(item.revenue)
          n_total_spends += parseInt(item.spends)
          n_total_subs += parseInt(item.subs)
          n_total_cps = n_total_subs > 0 ? Math.round((n_total_spends / n_total_subs)) : 0
          n_total_arpu = n_total_subs > 0 ? Math.round((n_total_revenue / n_total_subs)) : 0
        }
        
      });


      final_array.push({
        type: "Total Fresh Transactions",
        revenue: n_total_revenue.toString() || "0",
        spends: n_total_spends.toString() || "0",
        subs: n_total_subs.toString() || "0",
        cps: n_total_cps.toString() || "0",
        arpu: n_total_arpu.toString() || "0",
      });
      
      final_array.push({
        type: "Grand Total",
        revenue: total_revenue.toString() || "0",
        spends: total_spends.toString() || "0",
        subs: total_subs.toString() || "0",
        cps: total_cps.toString() || "0",
        arpu: total_arpu.toString() || "0",
      });

  

      return final_array
    };

    const result = renameKeys(result_data);

    res.json({
      success: true,
      data: {
        chart_name: "Acquisition",
        chart_data: result,
        daily_wise_data:
          req.body.daily_wise_data === true
            ? await chartEnabled(startDate, currentDate)
            : 0,
      },
    });
  } catch (ex) {}
};
