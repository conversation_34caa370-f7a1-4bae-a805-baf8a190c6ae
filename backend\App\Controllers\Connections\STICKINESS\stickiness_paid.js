const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");

exports.stickiness_paid = async (req, res) => {
  try {
    const { currentDate, startDate, pstartDate, pcurrentDate, utcpstartDate, utcpcurrentDate } = req.body;

    let {
      formattedCurrentDate,
      formattedPreviousDateStart,
      formattedPreviousPeriodStart,
    } = utc_adjust(startDate, currentDate);

    let cDataDauPaid = await QueryExecute(`
        WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios', 'web') AND location_properties_location_country='India' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' THEN custom_properties_device_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${startDate}T00:00:00' and '${currentDate}T23:59:59'
              AND event_name='Stream_Start' AND custom_properties_user_state='subscribed'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS paid_dau,
          COUNT(day) AS number_of_days
      FROM daily_data
       `);

       let pDataDauPaid = await QueryExecute(`
        WITH daily_data AS (
          SELECT
              toDate(timestamp) AS day,
              (COUNT(DISTINCT CASE WHEN device_properties_device_platform IN ('android','ios', 'web') AND location_properties_location_country='India' THEN user_id END))
              + COUNT(DISTINCT CASE WHEN device_properties_device_platform = 'external' AND custom_properties_country = 'IN' THEN custom_properties_device_id END) AS daily_dau
          FROM apxor
          WHERE
              timestamp between '${pstartDate}T00:00:00' and '${pcurrentDate}T23:59:59'
              AND event_name='Stream_Start' AND custom_properties_user_state='subscribed'
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
          GROUP BY day
          )
      SELECT
          SUM(daily_dau) / COUNT(day) AS paid_dau,
          COUNT(day) AS number_of_days
      FROM daily_data
       `);
   

    let cDataMauPaid = await QueryExecute(`
    SELECT
    android + ios + web + external AS paid_mau
    FROM
    (
        SELECT
            countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
            )) AS android,        
            countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
            )) AS ios,        
            countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
            )) AS web,        
            countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                custom_properties_device_id, NULL)) AS external        
        FROM apxor
        WHERE
            event_name = 'Stream_Start'
                    AND custom_properties_user_state='subscribed'
            AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${formattedPreviousDateStart} 18:30:00') 
            AND (parseDateTimeBestEffortOrNull(timestamp) <= '${formattedCurrentDate} 18:30:00'))
            ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
    ) AS counts
    `);

    let pDataMauPaid = await QueryExecute(`
      SELECT
      android + ios + web + external AS paid_mau
      FROM
      (
          SELECT
              countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
              )) AS android,        
              countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
              )) AS ios,        
              countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
              )) AS web,        
              countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                  custom_properties_device_id, NULL)) AS external        
          FROM apxor
          WHERE
              event_name = 'Stream_Start'
                      AND custom_properties_user_state='subscribed'
              AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${utcpstartDate} 18:30:00') 
              AND (parseDateTimeBestEffortOrNull(timestamp) <= '${utcpcurrentDate} 18:30:00'))
              ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
      ) AS counts
      `);

      // let dau_percent = 
      // ((cDataDauPaid[0].paid_dau - pDataDauPaid[0].paid_dau) / ( pDataDauPaid[0].paid_dau));

      // let mau_percent = ((cDataMauPaid[0].paid_mau - pDataMauPaid[0].paid_mau) / ( pDataMauPaid[0].paid_mau));

      //let percentage = (dau_percent / mau_percent) * 100

    let stick_cData_percent = (cDataDauPaid[0].paid_dau / cDataMauPaid[0].paid_mau) * 100

    let stick_cData = (cDataDauPaid[0].paid_dau / cDataMauPaid[0].paid_mau)

    let stick_pData = (pDataDauPaid[0].paid_dau / pDataMauPaid[0].paid_mau)

    let percentage = ((stick_cData - stick_pData) / stick_pData) * 100

    res.json({
      success: true,
      data: {
        chart_name: "Stickiness Paid",
        percentage: percentage,
        chart_data: stick_cData_percent,
      },
    });
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong" });
  }
};
