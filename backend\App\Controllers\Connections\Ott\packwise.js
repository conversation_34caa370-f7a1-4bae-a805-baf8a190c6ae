const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");
const { payment_gateway_india_map } = require("./payment_gateway_map");

const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];

const dailywise_data = async (startDate, currentDate, type, plan_title) => {
  try {
    let {
      formattedCurrentDate,
      formattedPreviousDateStart,
      formattedPreviousPeriodStart,
    } = utc_adjust(startDate, currentDate);

    let query = {
      query: {
        bool: {
          must: [
            {
              range: {
                created_at: {
                  gte: `${formattedPreviousDateStart}T18:30:00`,
                  lt: `${formattedCurrentDate}T18:29:59`,
                },
              },
            },
            { terms: { "payment_gateway.keyword": b2c_payment_gateways } },
            {
              terms: {
                txn_status: type === "new" ? [
                  "success", 
                  //"autorenewal_success",
                  "upgrade", 
                  "renewal", 
                  "autorenew"
                ] : type === "autorenewal" ? ["autorenewal_success"] : [
                  "success", 
                  "autorenewal_success", 
                  "upgrade", 
                  "renewal", 
                  "autorenew"]
              },
            },
            {
              match: {
                region: "IN",
              },
            },
            {
              match: {
                transaction_env: "production",
              },
            },
          ],
        },
      },
      aggs: {
        day_wise_report: {
          date_histogram: {
            field: "created_at",
            calendar_interval: "day",
            format: "yyyy-MM-dd",
            "time_zone": "Asia/Kolkata"
          },
          aggs: {
            payment_gateway_split: {
              terms: {
                field: "payment_gateway.keyword"
              },
              aggs: {
                group_by_period: {
                  terms: {
                    field: "period.keyword"
                  },
                  aggs: {
                    total_price_charged: {
                      sum: {
                        field: "price_charged"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      size: 0,
    }

    if (plan_title) {
      query.query.bool.must.push({
        "match": {
          "plan_title.keyword": plan_title
        }
      });
    }

    let d = await elk_excute("/shemaroo_user_plans/_search", query);

    // Transform the data to include net revenue calculations
    const transformedData = d.aggregations.day_wise_report.buckets.map(day => {
      const dayData = {
        key_as_string: day.key_as_string,
        group_by_period: {
          buckets: []
        }
      };

      // Create a map to aggregate data by period
      const periodMap = new Map();

      // Process each payment gateway's data
      day.payment_gateway_split.buckets.forEach(pg => {
        const pgCharges = payment_gateway_india_map[pg.key].PG;
        const gst = payment_gateway_india_map[pg.key].GST;

        pg.group_by_period.buckets.forEach(period => {
          const grossAmount = period.total_price_charged.value;
          const pgAmount = (grossAmount * pgCharges) / 100;
          const gstAmount = gst > 0 ? (grossAmount - grossAmount / 1.18) : 0;
          const netAmount = grossAmount - (pgAmount + gstAmount);

          if (!periodMap.has(period.key)) {
            periodMap.set(period.key, {
              key: period.key,
              doc_count: 0,
              total_price_charged: { value: 0 },
              net_revenue: 0
            });
          }

          const existingPeriod = periodMap.get(period.key);
          existingPeriod.doc_count += period.doc_count;
          existingPeriod.total_price_charged.value += grossAmount;
          existingPeriod.net_revenue += netAmount;
        });
      });

      // Convert the map to array and calculate final metrics
      dayData.group_by_period.buckets = Array.from(periodMap.values()).map(period => ({
        key: period.key,
        doc_count: period.doc_count,
        total_price_charged: { value: period.net_revenue }, // Using net revenue instead of gross
        arpu: period.doc_count > 0 ? Math.round(period.net_revenue / period.doc_count) : 0
      }));

      return dayData;
    });

    return {
      aggregations: {
        day_wise_report: {
          buckets: transformedData
        }
      }
    };

  } catch (Ex) {
    console.error("Error in dailywise_data:", Ex);
  }
};


exports.packwise = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate, type, plan_title } = req.body;

        let {
          formattedCurrentDate,
          formattedPreviousDateStart,
          formattedPreviousPeriodStart,
        } = utc_adjust(startDate, currentDate);

        let query = {
      "query": {
        "bool": {
          "must": [
            {
              "range": {
                "created_at": {
                  "gte": `${formattedPreviousDateStart}T18:30:00`,
                  "lt": `${formattedCurrentDate}T18:29:59`
                }
              }
            },
            { "terms": { "payment_gateway.keyword": b2c_payment_gateways } },
            {
              "terms": {
                "txn_status": type === "new" ? [
                  "success", 
                  //"autorenewal_success",
                  "upgrade", 
                  "renewal", 
                  "autorenew"
                ] : type === "autorenewal" ? ["autorenewal_success"] : [
                  "success", 
                  "autorenewal_success", 
                  "upgrade", 
                  "renewal", 
                  "autorenew"]
              }
            },
            {
              "match": {
                "region": "IN"
              }
            },
            {
              "match": {
                "transaction_env": "production"
              }
            }
          ]
        }
      },
      "aggs": {
        "payment_gateways": {
          "terms": {
            "field": "payment_gateway.keyword",
            "size": 100
          },
          "aggs": {
            "periods": {
              "terms": {
                "field": "period.keyword"
              },
              "aggs": {
                "total_price_charged": {
                  "sum": {
                    "field": "price_charged"
                  }
                }
              }
            }
          }
        }
      },
      "size": 0
    }

    if (plan_title) {
      query.query.bool.must.push({
        "match": {
          "plan_title.keyword": plan_title
        }
      });
    }

    let cData = await elk_excute("/shemaroo_user_plans/_search", query);

    function transformData(data) {
      const packSummary = {};
      let totalSubscription = 0;
      let totalRevenue = 0;
  
      // Function to calculate net revenue based on payment gateway percentages and GST
      function calculateNetRevenue(priceCharged, pg, gst) {
          let pgData = (priceCharged * pg) / 100;
          let gstData = gst > 0 ? (priceCharged - priceCharged / 1.18) : 0;
          let finalCut = pgData + gstData;
          return priceCharged - finalCut;
      }
  
      // Iterate over each provider
          data.payment_gateways.buckets.forEach(payment_gateway => {
              // const payment_gateway_ott = payment_gateway.key;
              payment_gateway.periods.buckets.forEach((plan_type) => {
                const pack = plan_type.key
                const subscription = plan_type.doc_count
                const priceCharged = plan_type.total_price_charged.value;
                const pg = payment_gateway_india_map[payment_gateway.key].PG;
                const gst = payment_gateway_india_map[payment_gateway.key].GST;

                const netRevenue = calculateNetRevenue(priceCharged, pg, gst);

                // Accumulate values for each pack
                if (!packSummary[pack]) {
                  packSummary[pack] = { subscription: 0, revenue: 0 };
                }
                packSummary[pack].subscription += subscription;
                packSummary[pack].revenue += netRevenue;

                // Accumulate total subscription and revenue
                totalSubscription += subscription;
                totalRevenue += netRevenue;
              });
          });
  
      // Transform the packSummary into an array with desired properties
      const output = Object.keys(packSummary).map(pack => {
          const subscription = packSummary[pack].subscription;
          const revenue = packSummary[pack].revenue;
          const arpu = (revenue / subscription).toFixed(0);  // Calculating ARPU
          const percentageContribution = ((subscription / totalSubscription) * 100).toFixed(2);  // Percentage contribution
  
          return {
              pack,
              subscription,
              revenue,
              arpu: Number(arpu),
              percentage_contribution: percentageContribution
          };
      });

      const totalPercentageContribution = output.reduce((sum, item) => sum + parseFloat(item.percentage_contribution), 0).toFixed(2);
  
      // Add the total summary row
      output.push({
          pack: "Total",
          subscription: totalSubscription,
          revenue: totalRevenue,
          arpu: totalSubscription > 0 ? (totalRevenue / totalSubscription).toFixed(0) : 0,
          percentage_contribution: isNaN(totalPercentageContribution) || totalPercentageContribution <= 0 ? "0" : Math.round(totalPercentageContribution, 2)
      });
  
      return output;
  }
  
  const data = transformData(cData.aggregations);

    let d1 =
      req.body.daily_wise_data === true
        ? await dailywise_data(startDate, currentDate, type, plan_title)
        : 0;
    let daily_wise_data = [];

    // First, collect all unique periods from the data
    const uniquePeriods = new Set();
    d1.aggregations.day_wise_report.buckets.forEach(bucket => {
        bucket.group_by_period.buckets.forEach(periodBucket => {
            uniquePeriods.add(periodBucket.key);
        });
    });

    // Now process the data with all periods for each date
    d1.aggregations.day_wise_report.buckets.forEach((v) => {
        const existingPeriods = new Map(
            v.group_by_period.buckets.map(bucket => [bucket.key, bucket])
        );

        // Add entry for each period, using existing data or zeros
        uniquePeriods.forEach(period => {
            const periodData = existingPeriods.get(period);
            daily_wise_data.push({
                created_at_time: v.key_as_string,
                period: period,
                subscription: periodData ? periodData.doc_count : 0,
                revenue: periodData ? periodData.total_price_charged.value : 0,
                arpu: periodData ? parseInt(periodData.total_price_charged.value / periodData.doc_count) : 0,
            });
        });
    });
    res.json({
      success: true,
      data: {
        chart_name: "Pack wise Data",
        "plan_title": plan_title || "all",
        chart_data: data,
        daily_wise_data: daily_wise_data,
      },
    });
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong" + ex });
  }
};
