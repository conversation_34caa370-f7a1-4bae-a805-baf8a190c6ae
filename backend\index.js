require("dotenv").config();
require("module-alias/register");
const express = require("express");
const cors = require("cors");
const path = require("path");
const PORT = process.env.PORT || 3001;
const indexRouter = require("./App/Routes/index");
const app = express();
const fs = require("fs");
const { client } = require("./App/Configs/redis");
const { formatResponse } = require("./App/Middleware/response_middlware");
const { setCache } = require("./App/Utils/Redis");
const { cacheMiddleware } = require("./App/Middleware/Redis/redis");
const { date_middleware } = require("./App/Middleware/Business_Dashboard/date_middlware");
app.use(cors());

// app.use(function (req, res, next) {
//   req.on("end", function () {
//       console.log('on request end',res);
//   });
//   next();
// });

// app.use((req, res, next) => {
//     res.setHeader('X-Custom-Header', 'This is a custom header');
//     next(); // Ensure the request continues to the next middleware or route handler
// });
app.use(express.json());



app.use((req, res, next) => {
  const originalJson = res.json.bind(res);
 
  res.json = async(data) => {
    let k = JSON.stringify(data);
    data.success && await setCache(req, k);
    return originalJson(data);
  };
  next();
});
// app.use(date_middleware)
// app.use(cacheMiddleware)



app.use(express.urlencoded({ extended: true }));
app.use(express.json({ limit: "100mb" }));
app.use(
  express.static(
    /*path.join(__dirname, */ "/home/<USER>/ShemDataLakeFrontend/build"
  )
);
app.use("/assets", express.static("./App/assets"));
const dotenv = require("dotenv");
const compression = require("compression");



app.use(compression({ threshold: 0.0001, level: 9 }));
dotenv.config();
app.use("/", indexRouter);

// app.get("*", (req, res) =>
//   res.json({ success: false, message: "route not found" })
// );
app.get("*", function (request, response) {
  const filePath = path.resolve(
    "/home/<USER>/ShemDataLakeFrontend/build",
    "index.html"
  );
  response.sendFile(filePath);

  fs.readFile(filePath, "utf8", function (err, data) {
    if (err) {
      return console.log(err);
    }
  });
});

//cronjobs()
app.listen(PORT, () => console.log(`🚀 Server Running On Port ${PORT}`));

module.exports = app;
