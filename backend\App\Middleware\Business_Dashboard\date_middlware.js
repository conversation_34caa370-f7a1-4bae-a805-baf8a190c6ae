const moment = require("moment");
const { cacheMiddleware } = require("../Redis/redis");

function adjustDates(reqBody) {
  let { currentDate, startDate } = reqBody;

  // Parse the dates
  let current = new Date(currentDate);
  let start = new Date(startDate);

  // Helper function to get the month difference
  function getMonthDifference(d1, d2) {
    return (
      (d1.getFullYear() - d2.getFullYear()) * 12 + d1.getMonth() - d2.getMonth()
    );
  }

  // Get the month difference
  let monthDifference = getMonthDifference(current, start);

  // Adjust the dates based on the month difference
  if (monthDifference == 0) {
    monthDifference = 1;
  }
  if (monthDifference !== 0) {
    // Helper function to subtract months from a date
    function subtractMonths(date, months) {
      let newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() - months);
      return newDate;
    }

    current = subtractMonths(current, Math.abs(monthDifference));
    start = subtractMonths(start, Math.abs(monthDifference));
  }

  // Format the dates back to yyyy-mm-dd
  function formatDate(date) {
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, "0");
    let day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  return {
    pcurrentDate: formatDate(current),
    pstartDate: formatDate(start),
  };
}
exports.date_middlewares = (req, res, next) => {
  try {
    let currentDate = req.body.end_date
      ? moment(new Date(req.body.end_date))
      : moment(new Date());
    let currentMoment = moment(currentDate);
    let prevDate = currentMoment
      .clone()
      .subtract(1, "days")
      .format("YYYY-MM-DD");
    let currentMonthStartDate = currentMoment
      .clone()
      .startOf("month")
      .format("YYYY-MM-DD");

    // Determine the end date based on date range (year, quarter, month)
    let startDate = req.body.start_date || currentMonthStartDate;

    currentDate = req.body.end_date || currentDate.format("YYYY-MM-DD");

    req.body = {
      ...req.body,
      currentDate,
      prevDate,
      startDate,
    };
    //let d=adjustDates(req.body)
    req.body = { ...req.body, ...adjustDates(req.body) };
    console.log(startDate, currentDate)
    next();
  } catch (Ex) {
    res.json({ success: false, message: "invaild date filter " + Ex });
  }
};
