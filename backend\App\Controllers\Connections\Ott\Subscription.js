const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const { utc_adjust } = require("../../../Utils/utc_time_change");
const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];
exports.Subscription = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate, pstartDate, pcurrentDate } = req.body;

    let {formattedCurrentDate,formattedPreviousDateStart,formattedPreviousPeriodStart} = utc_adjust(startDate, currentDate)

    let prevpstartDate = new Date(pstartDate)

    prevpstartDate.setDate(prevpstartDate.getDate() - 1)

    prevpstartDate = prevpstartDate.toISOString().split('T')[0]

    let cData = await elk_excute("/shemaroo_user_plans/_count", {
      query: {
        bool: {
          must: [
            {
              match: {
                region: "IN",
              },
            },
            { terms: { "payment_gateway.keyword": b2c_payment_gateways } },
            {
              match: {
                transaction_env: "production",
              },
            },
          ],
          filter: [
            {
              terms: {
                txn_status: [
                  "success",
                  "autorenewal_success",
                  "upgrade",
                  "renewal",
                  "autorenew",
                ],
              },
            },
            {
              range: {
                created_at: {
                  gte: `${formattedPreviousDateStart}T18:30:00`,
                  lt: `${formattedCurrentDate}T18:29:59`,
                },
              },
            },
          ],
        },
      },
    });

    let pData = await elk_excute("/shemaroo_user_plans/_count", {
      query: {
        bool: {
          must: [
            {
              match: {
                region: "IN",
              },
            },
            { terms: { "payment_gateway.keyword": b2c_payment_gateways } },

            {
              match: {
                transaction_env: "production",
              },
            },
          ],
          filter: [
            {
              terms: {
                txn_status: [
                  "success",
                  "autorenewal_success",
                  "upgrade",
                  "renewal",
                  "autorenew",
                ],
              },
            },
            {
              range: {
                created_at: {
                  gte: `${prevpstartDate}T18:30:00`,
                  lte: `${pcurrentDate}T18:29:59`,
                },
              },
            },
          ],
        },
      },
    });
    let subscription = parseInt(cData.count);
    let psubscription = parseInt(pData.count);
    let percentage = ((subscription - psubscription) / psubscription) * 100;

    res.json({
      success: true,
      data: {
        chart_name: "Subscriptions",
        percentage: percentage,
        chart_data: subscription
        // startDate === '2024-08-01' && currentDate === '2024-08-31' ? '48771':
        // subscription
      },
    });
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong" });
  }
};
