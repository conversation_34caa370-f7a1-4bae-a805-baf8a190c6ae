{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "_moduleAliases": {"@root": ".", "@error": "/backend/App/Error"}, "_moduleDirectories": ["node_modules_custom"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@clickhouse/client": "^0.2.10", "aws-sdk": "^2.1555.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "cronstrue": "^2.48.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "keycloak-connect": "^26.0.0", "loadash": "^1.0.0", "lodash": "^4.17.21", "module-alias": "^2.2.3", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "neo4j-driver": "^5.14.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "nodemon": "^3.0.1", "otp-generator": "^4.0.1", "pg": "^8.16.0", "redis": "^5.0.1"}}