const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { elk_excute } = require("../../../Utils/ELK/elk_excute");

const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];

exports.cps=async (req,res)=>{
    try{
      let {startDate, currentDate, prevDate, pstartDate, pcurrentDate} = req.body

      let cData_brand_spends = await QueryExecute(`select round(sum(toFloat64(brand_spends)),0) AS brand_spends from other_data where toDate(date) between '${startDate}' and '${currentDate}'`)

      let cData_performance_spends = await QueryExecute(`select sum(performance_spends) as performance_spends from (select round(toFloat64(gujarati_performance_spends) + toFloat64(non_gujarati_performance_spends),0) AS performance_spends from other_data where toDate(date) between '${startDate}' and '${currentDate}')`)

      let pData_brand_spends = await QueryExecute(`select round(sum(toFloat64(brand_spends)),0) AS brand_spends from other_data where toDate(date) between '${pstartDate}' and '${pcurrentDate}'`)

      let pData_performance_spends = await QueryExecute(`select sum(performance_spends) as performance_spends from (select round(toFloat64(gujarati_performance_spends) + toFloat64(non_gujarati_performance_spends),0) AS performance_spends from other_data where toDate(date) between '${pstartDate}' and '${pcurrentDate}')`)

      let cData = cData_performance_spends[0].performance_spends + cData_brand_spends[0].brand_spends

      let pData = pData_performance_spends[0].performance_spends + pData_brand_spends[0].brand_spends

      // let subscription = await  elk_excute("/shemaroo_user_plans/_count", {
      //   query: {
      //     bool: {
      //       must: [
      //         {
      //           match: {
      //             region: "IN",
      //           },
      //         },
      //         { terms: { "payment_gateway.keyword": b2c_payment_gateways } },
      //         {
      //           match: {
      //             transaction_env: "production",
      //           },
      //         },
      //       ],
      //       filter: [
      //         {
      //           terms: {
      //             txn_status: [
      //               "success",
      //               "autorenewal_success",
      //               "upgrade",
      //               "renewal",
      //               "autorenew",
      //             ],
      //           },
      //         },
      //         {
      //           range: {
      //             created_at: {
      //               gte: `${startDate}T00:00:00`,
      //               lte: `${currentDate}T23:59:59`,
      //             },
      //           },
      //         },
      //       ],
      //     },
      //   },
      // });
    
      // subscription = parseInt(subscription.count);

      // let psubscription = await  elk_excute("/shemaroo_user_plans/_count", {
      //   query: {
      //     bool: {
      //       must: [
      //         {
      //           match: {
      //             region: "IN",
      //           },
      //         },
      //         { terms: { "payment_gateway.keyword": b2c_payment_gateways } },
      //         {
      //           match: {
      //             transaction_env: "production",
      //           },
      //         },
      //       ],
      //       filter: [
      //         {
      //           terms: {
      //             txn_status: [
      //               "success",
      //               "autorenewal_success",
      //               "upgrade",
      //               "renewal",
      //               "autorenew",
      //             ],
      //           },
      //         },
      //         {
      //           range: {
      //             created_at: {
      //               gte: `${pstartDate}T00:00:00`,
      //               lte: `${pcurrentDate}T23:59:59`,
      //             },
      //           },
      //         },
      //       ],
      //     },
      //   },
      // });

      // psubscription = parseInt(psubscription.count);

      let c_total_perf_subs = await QueryExecute(
        `
          SELECT 
            sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Subs), '[^0-9.]', ''))) AS subs
          FROM cpsd
          WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
        `
      )

      let p_total_perf_subs = await QueryExecute(
        `
          SELECT 
            sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Total_Perf_Subs), '[^0-9.]', ''))) AS subs
          FROM cpsd
          WHERE Date BETWEEN '${pstartDate}' AND '${pcurrentDate}'
        `
      )

      let c_organic_subs = await QueryExecute(`
      SELECT 
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Subs), '[^0-9.]', ''))) AS subs
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
      `);  

      let p_organic_subs = await QueryExecute(`
      SELECT 
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM Organic_Subs), '[^0-9.]', ''))) AS subs
      FROM cpsd
      WHERE Date BETWEEN '${pstartDate}' AND '${pcurrentDate}'
      `);
      
      let c_auto_renewals_subs = await QueryExecute(`
      SELECT 
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Subs), '[^0-9.]', ''))) AS subs
      FROM cpsd
      WHERE Date BETWEEN '${startDate}' AND '${currentDate}'
      `);

      let p_auto_renewals_subs = await QueryExecute(`
      SELECT 
          sum(toFloat64(replaceRegexpAll(trim(BOTH ' ' FROM AR_Subs), '[^0-9.]', ''))) AS subs
      FROM cpsd
      WHERE Date BETWEEN '${pstartDate}' AND '${pcurrentDate}'
      `);

      subscription = parseInt(c_total_perf_subs[0].subs) + parseInt(c_organic_subs[0].subs) + parseInt(c_auto_renewals_subs[0].subs)

      psubscription = parseInt(p_total_perf_subs[0].subs) + parseInt(p_organic_subs[0].subs) + parseInt(p_auto_renewals_subs[0].subs)

      let cData_cps = subscription > 0 ? cData / subscription : 0

      let pData_cps = psubscription > 0 ? pData / psubscription : 0

      //let percentage = ((cData[0].cps - pData[0].cps) / pData[0].cps) * 100;

      let percentage = ((cData_cps - pData_cps) / pData_cps) * 100

      res.json({
        success: true,
        data: {
          chart_name: "CPS",
          percentage: percentage,//10.352,
          chart_data: cData_cps//cData[0].cps//4357
        },
      });
    }catch(ex)
    {
      res.json({ success: false, err: "Something went wrong " + ex });
    }
}