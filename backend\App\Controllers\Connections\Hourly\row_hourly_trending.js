const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const moment = require("moment-timezone");
const { payment_gateway_row_map } = require("../Ott/payment_gateway_map");
const { regionToINRMapping } = require("./currency_mapping");

const b2c_payment_gateways_row = [
    "apple_store",
    "google",
    "coupon",
    "google_play_tv",
    "stripe",
    "adyen",
];

const getAggregationsROW = async (startDate, endDate, type, region_data) => {
    return await elk_excute("/shemaroo_user_plans/_search", 
    {
      "query": {
          "bool": {
              "must": [
                  {
                      "range": {
                          "created_at": {
                              "gte": startDate,
                              "lt": endDate,
                          }
                      }
                  },
                  {
                      "terms": {
                          "payment_gateway.keyword": b2c_payment_gateways_row
                      }
                  },
                  {
                    terms: {
                      "txn_status.keyword": type === "new" ? [
                        "success",
                        //"autorenewal_success",
                        "upgrade",
                        "renewal",
                        "autorenew",
                      ] : type === "autorenewal" ? ["autorenewal_success"] : [
                        "success",
                        "autorenewal_success",
                        "upgrade",
                        "renewal",
                        "autorenew",
                      ],
                    },
                  },
                  region_data === "US" ? {
                      "match": {
                          "region.keyword": "US"
                      }
                  } : region_data === "CA" ? {
                      "match": {
                          "region.keyword": "CA"
                      }
                  } : {
                      "bool": {
                          "must_not": [
                              {
                                  "terms": {
                                      "region.keyword": [
                                          "IN",
                                          "US",
                                          "CA"
                                      ]
                                  }
                              }
                          ]
                      }
                  },
                  {
                      "match": {
                          "transaction_env.keyword": "production"
                      }
                  },
                  {
                      "terms": {
                          "period.keyword": [
                              "Monthly",
                              "Yearly",
                              "Quarterly",
                              "Two Yearly",
                              "Half Yearly"
                          ]
                      }
                  }
              ]
          }
      },
      "aggs": {
          "hourly_data": {
              "date_histogram": {
                  "field": "created_at",
                //   "calendar_interval": "hour"
                  "fixed_interval": "1h",
                  "time_zone": "Asia/Kolkata",
                  "min_doc_count": 0,
                  "extended_bounds": {
                        "min": startDate,
                        "max": endDate
                    }
              },
              "aggs": {
                  "periods": {
                      "terms": {
                          "field": "period.keyword"
                      },
                      "aggs": {
                          "payment_gateways": {
                              "terms": {
                                  "field": "payment_gateway.keyword"
                              },
                              "aggs": {
                                  "regions": {
                                      "terms": {
                                          "field": "region.keyword"
                                      },
                                      "aggs": {
                                          "total_price_charged": {
                                              "sum": {
                                                  "field": "price_charged"
                                              }
                                          }
                                      }
                                  }
                              }
                          }
                      }
                  }
              }
          },
          "daily_total": {
              "sum": {
                  "field": "price_charged"
              }
          }
      },
      "size": 0
  });
}

const processDataROW = (data) => {
  let result = { daywise: {} };
  
  data.aggregations.hourly_data.buckets.forEach((hourlyBucket) => {
    hourlyBucket.key_as_string = moment(hourlyBucket.key_as_string)
      .add(6, 'hours')
      .format('YYYY-MM-DDTHH:mm:ss[Z]');

    // Initialize hourly data even if there are no periods
    if (!result["daywise"][hourlyBucket.key_as_string]) {
      result["daywise"][hourlyBucket.key_as_string] = {
        transactions: 0,
        revenue: 0,
        // consolidated: [],
      };
    }

    // Skip processing if no periods, but keep the time slot
    if (!hourlyBucket.periods.buckets.length) return;

    hourlyBucket.periods.buckets.forEach((periodBucket) => {
      // Initialize period data if not exists
      if(!periodBucket.payment_gateways.buckets.length) return;

      if (!result[periodBucket.key]) {
        result[periodBucket.key] = { transactions: 0, revenue: 0 };
      }

      let periodNetRevenue = 0;
      // Calculate net revenue for each payment gateway
      periodBucket.payment_gateways.buckets.forEach((pgBucket) => {
        let region_inr_net_revenue = 0; // Initialize region net revenue for each payment gateway bucket
        pgBucket.regions.buckets.forEach((regionBucket) => {
            if (!regionBucket || !regionBucket.total_price_charged || typeof regionBucket.total_price_charged.value === 'undefined') {
                console.log(`Warning: Missing price data for region ${regionBucket ? regionBucket.key : 'unknown'}`);
                return; // Skip this region
              }
              // First check if regionToINRMapping exists and has the region
            if (regionToINRMapping && regionToINRMapping[regionBucket.key]) {
                region_inr_net_revenue += regionBucket.total_price_charged.value * regionToINRMapping[regionBucket.key];
              }
              // Last resort fallback to a hardcoded value
              else {
                console.log(`Warning: No conversion rate found for region ${regionBucket.key} and no US fallback`);
                region_inr_net_revenue += regionBucket.total_price_charged.value * regionToINRMapping["US"];
              }
        });

        const pg = payment_gateway_row_map[pgBucket.key].PG ;
        const gst = payment_gateway_row_map[pgBucket.key].GST;
        
        const grossAmount = region_inr_net_revenue; //pgBucket.total_price_charged.value; // Use region_inr_net_revenue instead of gross amount for net revenue calculation. This is because the gross amount is not always accurate.
        const pgAmount = (grossAmount * pg) / 100;
        const gstAmount = gst > 0 ? (grossAmount - grossAmount / 1.18) : 0;
        const netAmount = grossAmount - (pgAmount + gstAmount);
        
        periodNetRevenue += netAmount;
      });

      // Update period totals
      result[periodBucket.key].transactions += periodBucket.doc_count || 0;
      result[periodBucket.key].revenue += periodNetRevenue;

      // Initialize hourly data if not exists
    //   if (!result["daywise"][hourlyBucket.key_as_string]) {
    //     result["daywise"][hourlyBucket.key_as_string] = {
    //       transactions: 0,
    //       revenue: 0,
    //       consolidated: [],
    //     };
    //   }

      // Update hourly totals
      result["daywise"][hourlyBucket.key_as_string].transactions += periodBucket.doc_count || 0;
      result["daywise"][hourlyBucket.key_as_string].revenue += periodNetRevenue;
      
      // Add consolidated data
      result["daywise"][hourlyBucket.key_as_string]["consolidated"].push({
        transactions: periodBucket.doc_count,
        key: periodBucket.key,
        revenue: periodNetRevenue,
      });
    });
  });
  
  return result;
};

module.exports = {
  getAggregationsROW,
  processDataROW,
};
