// // keycloak initialization
// const Keycloak = require("keycloak-connect");
// const kcConfig = require("../../keycloak.json");
// const session = require("express-session");
// // const memoryStore = new session.MemoryStore();
// // const keycloak = new Keycloak({ store: memoryStore }, kcConfig);

// // Middleware to extract and log required fields from token details
// // const extractRequiredFields = (req, res, next) => {
// //   // Check if user is authenticated
// //   console.log("keycloak auth", req.kauth)
// //   if (req.kauth.grant) {
// //     // Extract required fields from token details
// //     const { preferred_username, sub, email, name } = req.kauth.grant.access_token.content;

// //     // Print the fields (optional, for logging purposes)
// //     console.log("Preferred Username:", preferred_username);
// //     console.log("User ID:", sub);
// //     console.log("Email:", email);

// //     // Attach the required fields to the request for future middleware or routes
// //     req.userDetails = {
// //       preferred_username,
// //       user_id: sub,
// //       email,
// //       name
// //     };
// //   }

// //   // Continue to the next middleware or route
// //   next();
// // };

// exports.keycloakinit = (router) => {
//   // console.log(keycloak)

//   const memoryStore = new session.MemoryStore();

//   router.use(
//     session({
//       secret: "saranyu_analytics",
//       resave: false,
//       saveUninitialized: true,
//       store: memoryStore,
//     })
//   );

//   const keycloak = new Keycloak({ store: memoryStore }, kcConfig, { debug: true });

//   // keycloak.middleware with keycloak.enforcer
//   router.use(keycloak.middleware({
//     logout: "/logout",
//     enforcer: {
//       response_mode: 'token',
//       // It was present in ETV, now removed for Shemaroo
//       // resource_server_id: 'your-resource-server-id',
//       // scopes: ['user:profile'],
//       resource_server_id: 'react-auth',
//       scopes: 'user:profile'
//     }
//   }));
//   // , (req, res, next) => {
//   //   console.log("Keycloak Logout middleware executed.");
//   //   // console.log("Request Headers:", req.headers);
//   //   next();
//   // });

//   // keycloak.enforcer access control handling
//   keycloak.accessDenied = (request, response) => {
//     console.log("Inside access denied")
//     response.json({ success: false, message: "Access denied" });
//   };

//   console.log("After access denied but before extractRequiredFields")
//   // Apply the extractRequiredFields middleware to all routes
//   // router.use(extractRequiredFields);

//   router.use((req, res, next) => {
//     // Check if user is authenticated
//     console.log("Keycloak auth:", req.kauth);
//     console.log("Auth Header", req.headers.authorization)

//     if (req.kauth && req.kauth.grant) {
//       // Extract required fields from token details
//       const { preferred_username, /* sub, email, name */ } = req.kauth.grant.access_token.content;

//       // Print the fields (optional, for logging purposes)
//       console.log("Preferred Username:", preferred_username);
//       // console.log("User ID:", sub);
//       // console.log("Email:", email);

//       // Attach the required fields to the request for future middleware or routes
//       req.userDetails = {
//         preferred_username,
//         // user_id: sub,
//         // email,
//         // name
//       };
//     } else {
//       console.warn("No grant available, user might not be authenticated.");
//     }

//     // Continue to the next middleware or route
//     next();
//   });

//   // Your routes go here

//   return keycloak;
// };

const Keycloak = require("keycloak-connect");
const session = require("express-session");
const kcConfig = require("../../keycloak.json");

exports.keycloakinit = (router) => {
  const memoryStore = new session.MemoryStore();

  router.use(
    session({
      secret: "saranyu_analytics",
      resave: false,
      saveUninitialized: true,
      store: memoryStore,
    })
  );

  const keycloak = new Keycloak({ store: memoryStore }, kcConfig, { debug: true });

  // Initialize Keycloak middleware
  router.use(keycloak.middleware());

  // Use Keycloak protection to force token validation
  // router.use(keycloak.protect());

  // Handle access denied
  keycloak.accessDenied = (req, res) => {
    console.log("Access denied for request:", req.originalUrl);
    res.status(403).json({ success: false, message: "Access denied" });
  };

    // Manually decode token for debugging
    router.use((req, res, next) => {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1]; // Extract token from Authorization header
        const jwt = require('jsonwebtoken');
        const decodedToken = jwt.decode(token, { complete: true });
        console.log('Decoded Token:', decodedToken);
      } else {
        console.warn('No Authorization Header');
      }
      next();
    });

  return keycloak;
};
