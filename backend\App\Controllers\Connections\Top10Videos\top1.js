const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");

const getFilterWise = async (
  filter_type,
  user_type,
  startDate,
  currentDate
) => {
  try {
    let data;

    if (user_type === "Paid") {
      if (filter_type === "all") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND custom_properties_user_state = 'subscribed'
            AND video_name <> ''
          GROUP BY video_name
          ORDER BY MOU DESC
          LIMIT 10
        `);
      } else if (filter_type === "gujurati") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND custom_properties_category_name = 'gujarati'
            AND custom_properties_user_state = 'subscribed'
            AND video_name<>''
          GROUP BY video_name
          ORDER BY MOU DESC
          LIMIT 10
        `);
      } else if (filter_type === "non-gujurati") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND custom_properties_category_name <> 'gujarati'
            AND custom_properties_user_state = 'subscribed'
            AND video_name<>''
          GROUP BY video_name
          ORDER BY Users DESC
          LIMIT 10
        `);
      }
    } else if (user_type === "Total") {
      if (filter_type === "all") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND video_name <> ''
          GROUP BY video_name
          ORDER BY MOU DESC
          LIMIT 10
        `);
      } else if (filter_type == "gujurati") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND custom_properties_category_name = 'gujarati'
            AND video_name<>''
          GROUP BY video_name
          ORDER BY MOU DESC
          LIMIT 10
        `);
      } else if (filter_type == "non-gujurati") {
        data = await QueryExecute(`
          SELECT 
            custom_properties_video_name AS video_name,
            COUNT(DISTINCT user_id) AS Users,
           SUM(custom_properties_value_new) / 60  AS MOU
          FROM default.apxor
          WHERE 
            event_name = 'Stream_End' AND (location_properties_location_country = 'India' OR custom_properties_country IN ('IN', [\'IN\']))
            AND (timestamp >= '${startDate}T00:00:00') AND (timestamp <= '${currentDate}T23:59:59')
            AND custom_properties_category_name <> 'gujarati'
            AND video_name<>''
          GROUP BY video_name
          ORDER BY MOU DESC
          LIMIT 10
        `);
      }
    }

    console.log("Data from function", data);

    return data;
  } catch (error) {
    console.error("Error in getFilterWise:", error);
    throw error;
  }
};

exports.top_10 = async (req, res) => {
  try {
    const { currentDate, startDate, user_type, filter_type } = req.body;
    let d = await getFilterWise(
      filter_type,
      user_type,
      startDate,
      currentDate
    );
    console.log("Top 10 Movies", d)
    // d.forEach((v) => {
    //   console.log(v)
    //   if (
    //     v.video_name === "Bachubhai" &&req.body.filter_type=='all'&&
    //     (startDate === "2024-08-01" && currentDate === "2024-08-31")
    //   ) {
       
    //     v.Users = 286709;
    //     }
    //     console.log(v)
    //   if (
    //     v.video_name === "Padosan" &&req.body.filter_type=='all'&&
    //     (startDate === "2024-08-01" && currentDate === "2024-08-31")
    //   ) {
    //     v.Users = 10912;
    //   }
    // });
    res.json({
      success: true,
      data: {
        chart_name: "Top 10 Movies",
        chart_data: d,
      },
    });
  } catch (error) {
    console.error("Error in top_10:", error);
    res.json({ success: false, err: "Something went wrong" });
  }
};
