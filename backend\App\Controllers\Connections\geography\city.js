const { elk_excute } = require("../../../Utils/ELK/elk_excute");
const b2c_payment_gateways = [
  "paytm",
  "paytm_cc",
  "paytm_dc",
  "paytm_upi",
  "razor_pay",
  "apple_store",
  "paytm_qr_code",
  "call_center",
  "amazon_pay_tv",
  "paytm_link",
  "google",
  "paytm_qr_tv",
  "amazon_pay_web",
  "coupon",
  "google_play_tv",
  "stripe",
  "paypal",
  "juspay",
];
exports.city_transaction = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate } = req.body;
    let q = await elk_excute("/shemaroo_user_plans/_search", {
      query: {
        bool: {
          must: [
            { match: { region: "IN" } },
            { terms: { "payment_gateway.keyword": b2c_payment_gateways } },
            {
              range: {
                created_at: {
                  gte: `${prevDate}T18:30:00`,
                  lt: `${currentDate}T18:29:59`,
                },
              },
            },
            {
              terms: {
                txn_status: req.body.type === "new" ? [
                  "success",
                  //"autorenewal_success",
                  "upgrade",
                  "renewal",
                  "autorenew",
                ] : req.body.type === "autorenewal" ? ["autorenewal_success"] : [
                  "success",
                  "autorenewal_success",
                  "upgrade",
                  "renewal",
                  "autorenew",
                ],
              },
            },
            { match: { transaction_env: "production" } },
          ],
        },
      },
      aggs: {
        state_counts: {
          terms: {
            field: "state.keyword",
            size: 100,
            order: { _count: "desc" },
          },
        },
      },
      size: 0,
    });
    let d = [];
    let s = 0;
    q.aggregations.state_counts.buckets
      .sort((a, b) => b.doc_count - a.doc_count)
      .slice(0, 10)
      .forEach((v) => {
        if (v.key === "") {
          s = s + v.doc_count;
        } else if (v.key != "" || v.key != "") {
          d.push({ city: v.key, transaction: v.doc_count });
        }
      });

    let l = q.aggregations.state_counts.buckets.length - 1;
    let k = l > 10 ? 10 : l - 1;
    q.aggregations.state_counts.buckets.slice(k, l).forEach((v) => {
      s = s + parseInt(v.doc_count);
    });
    d.push({ city: "others", transaction: s });

    res.json({ success: true, data: d, re: q, prevDate, currentDate });
  } catch (ex) {
    res.json(ex);
  }
};
