const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");

const { utc_adjust } = require("../../../Utils/utc_time_change");

exports.mau = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate,pcurrentDate,pstartDate, utcpstartDate, utcpcurrentDate } = req.body;

    let {
      formattedCurrentDate,
      formattedPreviousDateStart,
      formattedPreviousPeriodStart,
    } = utc_adjust(startDate, currentDate);

    console.log("Previous Date Range MAU", formattedPreviousPeriodStart, formattedPreviousDateStart)

    let cData_query = `
    SELECT    
        android + ios + web + external AS mau
    FROM
    (
    SELECT
        countDistinct(IF((device_properties_device_platform = 'android') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS android,
        countDistinct(IF((device_properties_device_platform = 'ios') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS ios,
        countDistinct(IF((device_properties_device_platform = 'web') AND (event_name = 'APX_PAGE_OPENED') AND (location_properties_location_country = 'India'), user_id, NULL)) AS web,
        countDistinct(IF((device_properties_device_platform = 'external') AND (event_name = 'Stream_Start') AND (custom_properties_country = 'IN'), custom_properties_device_id, NULL)) AS external
    FROM apxor
    WHERE ((event_name = 'apx_app_opened' AND device_properties_device_platform IN ('android', 'ios')) 
        OR (event_name = 'APX_PAGE_OPENED' AND device_properties_device_platform = 'web') 
        OR (event_name = 'Stream_Start' AND device_properties_device_platform = 'external'))
        AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${formattedPreviousDateStart} 18:30:00') 
        AND (parseDateTimeBestEffortOrNull(timestamp) <= '${formattedCurrentDate} 18:30:00'))
        ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
      ) AS counts
    `;

    let pData_query = `
    SELECT    
        android + ios + web + external AS mau
    FROM
    (
    SELECT
        countDistinct(IF((device_properties_device_platform = 'android') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS android,
        countDistinct(IF((device_properties_device_platform = 'ios') AND (event_name = 'apx_app_opened') AND (location_properties_location_country = 'India'), user_id, NULL)) AS ios,
        countDistinct(IF((device_properties_device_platform = 'web') AND (event_name = 'APX_PAGE_OPENED') AND (location_properties_location_country = 'India'), user_id, NULL)) AS web,
        countDistinct(IF((device_properties_device_platform = 'external') AND (event_name = 'Stream_Start') AND (custom_properties_country = 'IN'), custom_properties_device_id, NULL)) AS external
    FROM apxor
    WHERE ((event_name = 'apx_app_opened' AND device_properties_device_platform IN ('android', 'ios')) 
        OR (event_name = 'APX_PAGE_OPENED' AND device_properties_device_platform = 'web') 
        OR (event_name = 'Stream_Start' AND device_properties_device_platform = 'external'))
        AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${utcpstartDate} 18:30:00') 
        AND (parseDateTimeBestEffortOrNull(timestamp) < '${utcpcurrentDate} 18:30:00'))
        ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
      ) AS counts
    `;

    let [cData, pData] = await Promise.all([
      QueryExecute(cData_query),
      QueryExecute(pData_query),
    ]);

    // let cData = await QueryExecute(`
    //   SELECT
    //   (SUM(dau_android_ios) + SUM(dau_external) + SUM(dau_web)) AS mau
    //   FROM default.apxor_dau_mau_daily_view
    //   WHERE (event_date >= '${startDate}') AND (event_date <= '${currentDate}')
    // `);

    // let pData = await QueryExecute(`
    //    SELECT
    //   (SUM(dau_android_ios) + SUM(dau_external) + SUM(dau_web)) AS mau
    //   FROM default.apxor_dau_mau_daily_view
    //   WHERE (event_date >= '${startDate}') AND (event_date <= '${prevDate}')
    // `);

    let percentage = ((cData[0].mau - pData[0].mau) / pData[0].mau) * 100;
    let data = {
      chart_name: "MAU",
      percentage: parseFloat(percentage),
      chart_data: cData[0].mau,
    };
    let s = {
      success: true,
      data: data,
    };
    //  let k1 = JSON.stringify(s);
    //  await setCache(req, k1);
    res.json(s);
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong " + ex });
  }
};
