const { QueryExecute } = require("../../../Utils/clickhouse/clickhouse_excute");
const { setCache } = require("../../../Utils/Redis");
const { utc_adjust } = require("../../../Utils/utc_time_change");

exports.paid_mau = async (req, res) => {
  try {
    const { currentDate, prevDate, startDate, pstartDate, pcurrentDate, utcpstartDate, utcpcurrentDate } = req.body;

    let {
      formattedCurrentDate,
      formattedPreviousDateStart,
      formattedPreviousPeriodStart,
    } = utc_adjust(startDate, currentDate);

    let cData_query = `
    SELECT
    android + ios + web + external AS paid_mau
    FROM
    (
        SELECT
            countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
            )) AS android,        
            countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
            )) AS ios,        
            countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
            )) AS web,        
            countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                custom_properties_device_id, NULL)) AS external        
        FROM apxor
        WHERE
            event_name = 'Stream_Start'
                    AND custom_properties_user_state='subscribed'
            AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${formattedPreviousDateStart} 18:30:00') 
            AND (parseDateTimeBestEffortOrNull(timestamp) <= '${formattedCurrentDate} 18:30:00'))
            ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
    ) AS counts
    `

    let pData_query = `
    SELECT
    android + ios + web + external AS paid_mau
    FROM
    (
        SELECT
            countDistinct(IF(device_properties_device_platform = 'android' AND location_properties_location_country = 'India', user_id, NULL
            )) AS android,        
            countDistinct(IF(device_properties_device_platform = 'ios' AND location_properties_location_country = 'India', user_id, NULL
            )) AS ios,        
            countDistinct(IF(device_properties_device_platform = 'web' AND location_properties_location_country = 'India', user_id, NULL
            )) AS web,        
            countDistinct(IF(device_properties_device_platform = 'external' AND custom_properties_country = 'IN', 
                custom_properties_device_id, NULL)) AS external        
        FROM apxor
        WHERE
            event_name = 'Stream_Start'
                    AND custom_properties_user_state='subscribed'
                    AND ((parseDateTimeBestEffortOrNull(timestamp) >= '${utcpstartDate} 18:30:00') 
            AND (parseDateTimeBestEffortOrNull(timestamp) < '${utcpcurrentDate} 18:30:00'))
             ${req.body.category === 'gujarati' ? `AND custom_properties_category_name ILIKE ('gujarati%')` : req.body.category === 'non_gujarati' ? `AND custom_properties_category_name IN  ('bollywood_plus', 'all', 'marathi', 'bollywood_classic', 'red_hot', 'bhakti', 'kids', 'bengali', 'comedy')` : ''}
    ) AS counts
    `

    let [cData, pData] = await Promise.all([QueryExecute(cData_query), QueryExecute(pData_query)])

    // let cData = await QueryExecute(`
    //   SELECT
    //   (SUM(pdau_web_android_ios) + SUM(pdau_external)) AS paid_mau
    //   FROM default.apxor_dau_mau_daily_view
    //   WHERE (event_date >= '${startDate}') AND (event_date <= '${currentDate}')
    // `);

    // let pData = await QueryExecute(`
    //    SELECT
    //    (SUM(pdau_web_android_ios) + SUM(pdau_external)) AS paid_mau
    //    FROM default.apxor_dau_mau_daily_view
    //    WHERE (event_date >= '${startDate}') AND (event_date <= '${prevDate}')
    // `);

    let percentage =
      ((cData[0].paid_mau - pData[0].paid_mau) / ( pData[0].paid_mau)) * 100;
    let data={
      chart_name: "Paid MAU",
      percentage: parseFloat(percentage),
      chart_data: cData[0].paid_mau,
    }  
      let s = {
        success: true,
        data: data,
      };
      // let k1 = JSON.stringify(s);
      // await setCache(req, k1);
      res.json(s);
   
  } catch (ex) {
    res.json({ success: false, err: "Something went wrong " + ex });
  }
};
