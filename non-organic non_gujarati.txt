-- non-organic non_gujarati
INSERT INTO default.ott_lake_test
SELECT
    old.id,
    old.plan_id,
    old.region,
    old.category,
    old.valid_till,
    old.start_date,
    old.user_id,
    old.content_ids,
    old.catalog_id,
    old.old_transaction_id,
    old.created_at,
    old.updated_at,
    old.reserved1,
    old.reserved2,
    old.subscription_id,
    old.plan_categories,
    old.billing_date,
    old.free_trial_start_date,
    old.free_trial_pack,
    old.device_info,
    old.auto_renew,
    old.plan_status,
    old.plan_modified,
    old.transaction_id,
    old.free_trial_end_date,
    old.txn_status,
    old.payment_gateway,
    old.period,
    old.mode,
    old.order_id,
    old.transaction_env,
    old.amount,
    old.price_charged,
    old.cancel_date,
    old.coupon_code,
    old.coupon_code_id,
    old.revenue,
    old.email_id,
    old.ext_account_email_id,
    old.user_analytical_id,
    old.user_first_purchase_date,
    old.plan_title,
    old.ext_authenticator,
    old.old_plan_id,
    old.old_sub_id,
    old.old_userplan_id,
    old.discount_offer,
    old.upgrade_date,
    old.renewal_date,
    old.pro_rata,
    old.agent_id,
    old.cancel_reason,
    old.invoice,
    old.invoice_id,
    old.state,
   CASE
    WHEN (old.type IS NULL OR old.type = '') AND old.coupon_campaign IS NOT NULL AND old.coupon_campaign != '' THEN old.coupon_campaign
    WHEN old.type IS NULL OR old.type = '' THEN COALESCE(temp.type, old.type)
    ELSE old.type
   END AS type,
   CASE
        WHEN (old.perf_type IS NULL OR old.perf_type = '') AND old.coupon_category IS NOT NULL AND old.coupon_category != '' THEN old.coupon_category
        WHEN old.perf_type IS NULL OR old.perf_type = '' THEN COALESCE(temp.perf_type, old.perf_type)
        ELSE old.perf_type
    END AS perf_type,
    old.coupon_category,
	old.coupon_campaign
FROM ott_lake_test AS old
INNER JOIN (
    WITH combined_data AS ( 
	-- app edgy
     SELECT 
                        event_value_order_id AS order_id
                FROM appsflyer_flat_app
                WHERE event_time BETWEEN '2025-06-01 00:00:00' AND '2025-06-30 23:59:59'
                        AND is_primary_attribution = 'True'
                        AND country_code='IN'
                        AND media_source IN ('google', 'GoogleAds', 'googleadwords_int')
                        AND partner = 'interactiveavenues'
                        AND campaign ILIKE '%edgy%'
                        AND event_name = 'PURCHASE' GROUP BY order_id
        UNION ALL
        SELECT 
         event_value_order_id AS order_id
                FROM appsflyer_flat_app
                WHERE event_time BETWEEN '2025-06-01 00:00:00' AND '2025-06-30 23:59:59'
                        AND is_primary_attribution = 'True'
                        AND country_code='IN' 
                        AND media_source IN ('Facebook Ads', 'facebook', 'Instagram','FacebookAds')
                        AND partner = 'interactiveavenues'
                        AND campaign ILIKE '%edgy%'
                        AND event_name = 'PURCHASE' GROUP BY order_id
        UNION ALL
        SELECT 
                         event_value_order_id AS order_id
                FROM appsflyer_flat_app
                WHERE event_time BETWEEN '2025-06-01 00:00:00' AND '2025-06-30 23:59:59'
                        AND is_primary_attribution = 'True'
                        AND country_code='IN' 
                        AND media_source IN ('fb', 'restricted', 'FBAds', 'Facebook Ads','FacebookAds')
                        AND (partner = '' OR partner IS NULL)
                        AND state != 'GJ'
                        AND city NOT IN ('Thane', 'Mumbai')
                        AND event_name = 'PURCHASE' GROUP BY order_id
        UNION ALL

        SELECT 
                         event_value_order_id AS order_id
                         from appsflyer_flat_app
        where event_time BETWEEN '2025-06-01 00:00:00' and '2025-06-30 23:59:59'
        AND is_primary_attribution = 'True'
        AND country_code='IN' 
        AND (partner != '' or partner is NOT NULL)
        AND partner !='interactiveavenues'
        AND event_name ='PURCHASE' GROUP BY order_id
        
        UNION ALL
        
        SELECT 
                         event_value_order_id AS order_id
        from appsflyer_flat_app
        where event_time BETWEEN '2025-06-01 00:00:00' and '2025-06-30 23:59:59'
        AND is_primary_attribution = 'True'
        AND country_code='IN' 
        AND (partner = '' or partner is NULL)
        AND media_source like '%_int'
        AND event_name ='PURCHASE' GROUP BY order_id
		  
UNION ALL
	-- Web edgy
	SELECT 
        event_value_order_id AS order_id
    FROM appsflyer_flat_web 
    WHERE event_time BETWEEN '2025-06-01 00:00:00' AND '2025-06-30 23:59:59'
    AND media_source IN ('googleads', 'googleadwords_int','google adwords')
    AND original_url IS NOT NULL
    AND (state != 'GJ' OR state IS NULL)
    AND (city IS NULL OR city NOT IN ('Thane', 'Mumbai', 'Navimumbai'))
    AND event_name IN ('PURCHASE')
    GROUP BY order_id
	
    UNION ALL
	
   SELECT 
        event_value_order_id AS order_id
    FROM appsflyer_flat_web 
     WHERE event_time BETWEEN '2025-06-01 00:00:00' and '2025-06-30 23:59:59'
        AND media_source IN ('facebook', 'fb', 'facebook-paid','fbads','instagram','facebook-organic','facebookads')
         AND original_url is NOT NULL
                AND (state != 'GJ' OR state IS NULL)
        AND (city IS NULL OR city NOT IN ('Thane', 'Mumbai', 'Navimumbai'))
                AND (event_name = 'PURCHASE' )
    GROUP BY order_id
	
    UNION ALL
	
     SELECT  event_value_order_id AS order_id
    FROM appsflyer_flat_web 
     WHERE event_time BETWEEN '2025-06-01 00:00:00' and '2025-06-30 23:59:59'
            AND media_source = 'paid'
        AND (JSONExtractString(query_params, 'utm_medium')='edgy' OR JSONExtractString(query_params, 'utm_medium')='bollywood')
                AND (event_name = 'PURCHASE' )
    GROUP BY order_id
        
        UNION ALL
		
      SELECT  event_value_order_id AS order_id
    FROM appsflyer_flat_web 
    WHERE event_time BETWEEN '2025-06-01 00:00:00' and '2025-06-30 23:59:59'
        AND media_source IN ('zoutons')
        AND (event_name = 'PURCHASE' )               
        GROUP BY order_id	 
      ),
      successful_orders AS (
          SELECT 
              order_id
          FROM ott_lake_test FINAL
          WHERE txn_status IN ('success', 'autorenewal_success', 'upgrade', 'renewal', 'autorenew')
              AND parseDateTimeBestEffortOrNull(created_at) BETWEEN '2025-05-31 18:30:00' AND '2025-06-30 18:30:00'
			  AND payment_gateway IN ('paytm', 'paytm_cc', 'paytm_dc', 'paytm_upi', 'razor_pay', 'apple_store', 'paytm_link', 'google', 'amazon_pay_web', 'coupon',  'stripe', 'paypal', 'juspay')
	AND region = 'IN' AND transaction_env = 'production' AND (type = '' OR type IS NULL)
      )
      SELECT
	  so.order_id AS order_id,
      'non_organic' AS type,
	  'non_gujarati' AS perf_type
      FROM combined_data cd
      JOIN successful_orders so
          ON so.order_id = cd.order_id 
) AS temp
ON old.order_id = temp.order_id