--autorenewal
INSERT INTO default.ott_lake_test
SELECT
    old.id,
    old.plan_id,
    old.region,
    old.category,
    old.valid_till,
    old.start_date,
    old.user_id,
    old.content_ids,
    old.catalog_id,
    old.old_transaction_id,
    old.created_at,
    old.updated_at,
    old.reserved1,
    old.reserved2,
    old.subscription_id,
    old.plan_categories,
    old.billing_date,
    old.free_trial_start_date,
    old.free_trial_pack,
    old.device_info,
    old.auto_renew,
    old.plan_status,
    old.plan_modified,
    old.transaction_id,
    old.free_trial_end_date,
    old.txn_status,
    old.payment_gateway,
    old.period,
    old.mode,
    old.order_id,
    old.transaction_env,
    old.amount,
    old.price_charged,
    old.cancel_date,
    old.coupon_code,
    old.coupon_code_id,
    old.revenue,
    old.email_id,
    old.ext_account_email_id,
    old.user_analytical_id,
    old.user_first_purchase_date,
    old.plan_title,
    old.ext_authenticator,
    old.old_plan_id,
    old.old_sub_id,
    old.old_userplan_id,
    old.discount_offer,
    old.upgrade_date,
    old.renewal_date,
    old.pro_rata,
    old.agent_id,
    old.cancel_reason,
    old.invoice,
    old.invoice_id,
    old.state,
   CASE
    WHEN (old.type IS NULL OR old.type = '') AND old.coupon_campaign IS NOT NULL AND old.coupon_campaign != '' THEN old.coupon_campaign
    WHEN old.type IS NULL OR old.type = '' THEN COALESCE(temp.type, old.type)
    ELSE old.type
   END AS type,
   CASE
        WHEN (old.perf_type IS NULL OR old.perf_type = '') AND old.coupon_category IS NOT NULL AND old.coupon_category != '' THEN old.coupon_category
        WHEN old.perf_type IS NULL OR old.perf_type = '' THEN COALESCE(temp.perf_type, old.perf_type)
        ELSE old.perf_type
    END AS perf_type,
    old.coupon_category,
	old.coupon_campaign
FROM ott_lake_test AS old
INNER JOIN (
    WITH combined_data AS ( 
          SELECT 
		
        distinct(a.order_id) AS order_id,
        a.user_analytical_id, 
		b.custom_properties_custom_user_id,
		a.period,
        		
        -- Calculate Gujarati watch time
        SUM(CASE WHEN b.custom_properties_category_name = 'gujarati' 
                 THEN COALESCE(CAST(b.custom_properties_value_new AS Float64), 0) 
                 ELSE 0 END) AS gujarati_watch_time,
                 
        -- Calculate non-Gujarati watch time
        SUM(CASE WHEN b.custom_properties_category_name != 'gujarati' 
                 THEN COALESCE(CAST(b.custom_properties_value_new AS Float64), 0) 
                 ELSE 0 END) AS other_watch_time,
                 
        -- Calculate total watch time
        SUM(COALESCE(CAST(b.custom_properties_value_new AS Float64), 0)) AS total_watch_time,
        
        -- Classify users based on watch time or 'Two Yearly' period
        CASE	
		
		   -- Case 1: NO stream end mark them gujarati 
			WHEN (b.custom_properties_custom_user_id IS NULL OR b.custom_properties_custom_user_id='')  THEN 'Not_Streamed'
					
			
             -- Case 2: Total watch time is more than 5 minutes and Gujarati watch time is 25% or more of the total watch time
            WHEN SUM(CAST(b.custom_properties_value_new AS Float64)) > 300 AND a.period != 'Two Yearly' -- total watch time > 5 minutes
                 AND SUM(CASE WHEN b.custom_properties_category_name = 'gujarati' 
                              THEN CAST(b.custom_properties_value_new AS Float64) 
                              ELSE 0 END) / SUM(CAST(b.custom_properties_value_new AS Float64)) >= 0.25 
            THEN 'gujarati'            
            
            -- Case 3: Total watch time is 5 minutes, and Gujarati watch time is 50% or more
            WHEN SUM(CAST(b.custom_properties_value_new AS Float64)) <= 300 AND a.period != 'Two Yearly'
                 AND SUM(CASE WHEN b.custom_properties_category_name = 'gujarati' 
                              THEN CAST(b.custom_properties_value_new AS Float64) 
                              ELSE 0 END) / SUM(CAST(b.custom_properties_value_new AS Float64)) >= 0.5 
             THEN 'gujarati'			


            -- Case 4: The subscription period is 'Two Yearly'
           -- WHEN a.period = 'Two Yearly' THEN 'gujarati'			
			  

            -- Default case
            ELSE 'non_gujarati'
        END AS user_type	
    FROM (SELECT * FROM ott_lake_test FINAL) a
    LEFT JOIN (
        SELECT 
            custom_properties_custom_user_id,
            custom_properties_category_name, 
            custom_properties_value_new,  
            timestamp
        FROM apxor 
        WHERE timestamp BETWEEN '2025-06-01 00:00:00' AND '2025-06-30 23:59:59'
          AND event_name = 'Stream_End'
    ) b
    ON a.user_analytical_id = b.custom_properties_custom_user_id  
      
    WHERE a.txn_status IN ('autorenewal_success')
      AND parseDateTimeBestEffortOrNull(a.created_at) BETWEEN '2025-05-31 18:30:00' AND '2025-06-30 18:30:00'
      AND a.payment_gateway IN ('paytm', 'paytm_cc', 'paytm_dc', 'paytm_upi', 
                                'razor_pay', 'apple_store', 'paytm_qr_code', 
                                'call_center', 'amazon_pay_tv', 'paytm_link', 
                                'google', 'paytm_qr_tv', 'amazon_pay_web', 
                                'coupon', 'google_play_tv', 'stripe', 'paypal', 'juspay')
      AND a.region = 'IN' 
      AND a.transaction_env = 'production'
	  AND (a.type = '' OR a.type IS NULL) 
      
    GROUP BY b.custom_properties_custom_user_id, order_id, a.user_analytical_id,a.period
      )
      SELECT
	  cd.order_id AS order_id,
      'auto_renewal' AS type,
	  user_type AS perf_type
      FROM combined_data cd
) AS temp
ON old.order_id = temp.order_id